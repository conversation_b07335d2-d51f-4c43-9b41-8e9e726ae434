Great, so we have this:


<!-- codebase -->
<!-- file -->
.dockerignore
```
Dockerfile
.dockerignore
node_modules
npm-debug.log
README.md
.env
.nyc_output
coverage
.git
.gitignore
build
.react-router
```
<!-- end of file -->

<!-- file -->
.env
```
NODE_ENV=development
PORT=3000

# API URL for the client to connect to the server
VITE_API_URL=http://localhost:3001

```
<!-- end of file -->

<!-- file -->
.env.example
```
NODE_ENV=development
PORT=3000

# API URL for the client to connect to the server
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001
```
<!-- end of file -->

<!-- file -->
C<PERSON><PERSON><PERSON>le
```
{
	# global options
	admin off # theres no need for the admin api in railway's environment
	persist_config off # storage isn't persistent anyway
	auto_https off # railway handles https for us, this would cause issues if left enabled
	log {
		# runtime logs
		format json # set runtime log format to json mode 
	}
	servers {
		# server options
		trusted_proxies static private_ranges # trust railway's proxy
	}
}
:{$PORT} {
    log {
        format json
    }

    # Set the root directory to the static build output
    root * /srv

    # Rewrite all non-file requests to index.html for SPA routing
    try_files {path} /index.html

    # Set appropriate caching headers for static assets
    header {
        Cache-Control "public, max-age=********, immutable"
    }

    # Serve the static files, using pre-compressed assets if available
    file_server {
        precompressed zstd gzip
    }
}

```
<!-- end of file -->

<!-- file -->
Dockerfile
```
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
COPY apps/www/package.json ./apps/www/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the www app for production
RUN cd apps/www && npm run build

# Use a minimal Caddy image for the final stage
FROM caddy:2-alpine AS runner
WORKDIR /srv

# Copy Caddy configuration and the built static assets
COPY apps/www/Caddyfile /etc/caddy/Caddyfile
COPY --from=builder /app/apps/www/build/client .
```
<!-- end of file -->

<!-- file -->
Dockerfile.dev
```
# Development Dockerfile for React Router Frontend
FROM node:20-alpine

# Install system dependencies
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package.json package-lock.json* ./
COPY apps/www/package.json ./apps/www/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Set working directory to www app
WORKDIR /app/apps/www

# Expose port
EXPOSE 3000

# Set environment
ENV NODE_ENV=development
ENV PORT=3000

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 reactrouter
USER reactrouter

# Start development server with hot reload
CMD ["npm", "run", "dev"]

```
<!-- end of file -->

<!-- file -->
app/app.css
```css
@import "tailwindcss";
@import "tw-animate-css";
@import "katex/dist/katex.min.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: "Geist Sans", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: "Geist Mono", ui-monospace, monospace, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}


/* Essential light theme enforcement */
* {
  color-scheme: light only;
}

@media (prefers-color-scheme: dark) {
  * {
    color-scheme: light !important;
  }
}

/* Improved mobile touch handling */
@media (max-width: 1023px) {
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground tracking-normal;
  }

  /* Custom Scrollbar Styles using Theme Variables */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--muted);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--ring);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }
}
```
<!-- end of file -->

<!-- file -->
app/auth/auth-provider.tsx
```xml
// apps/www/app/auth/auth-provider.tsx (fixed types)
import React, { createContext, useState, useEffect, useCallback } from 'react';
import { apiClient } from '../utils/axios-client';

interface User {
  id: string;
  email: string;
  name: string;
  picture?: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (messageToSave?: string) => void;
  logout: () => void;
  fetchUser: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUser = useCallback(async () => {
    setIsLoading(true);
    try {
      const userData = await apiClient.get<User>('/api/auth/profile');
      if (userData) {
        setUser(userData);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      await apiClient.post('/api/auth/refresh');
      await fetchUser();
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }, [fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  const login = (messageToSave?: string) => {
    if (messageToSave) {
      localStorage.setItem('unsentChatMessage', messageToSave);
    }
    window.location.href = '/api/auth/google';
  };

  const logout = async () => {
    try {
      await apiClient.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      window.location.reload();
    }
  };

  const value = {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
    fetchUser,
    refreshTokens,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

```
<!-- end of file -->

<!-- file -->
app/auth/useAuth.ts
```xml
import { useContext } from 'react';
import { AuthContext } from './auth-provider';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

```
<!-- end of file -->

<!-- file -->
app/components/auto-textarea.tsx
```xml
import * as React from 'react';
import { clsx } from 'clsx';

export interface AutoTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  onEnterKeyPress?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

export type AutoTextareaRef = {
  reset: () => void;
  focus: () => void;
};

const AutoTextarea = React.forwardRef<AutoTextareaRef, AutoTextareaProps>(
  (
    { className, onEnterKeyPress, onChange, onKeyDown, value, ...props },
    ref,
  ) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);

    // Merge refs
    React.useImperativeHandle(ref, () => ({
      reset: () => {
        if (textareaRef.current) {
          textareaRef.current.style.height = '36px';
        }
      },
      focus: () => {
        textareaRef.current?.focus();
      },
    }));

    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`; // Max height 200px
      }
    }, []);

    const handleKeyDown = React.useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        let shouldSendMessage = false;

        if (e.key === 'Enter') {
          const hasLineBreaks = (
            e.target as HTMLTextAreaElement
          ).value.includes('\n');

          if (e.shiftKey) {
            // Default behavior: new line. Do nothing special.
          } else if (e.ctrlKey || e.metaKey) {
            // Always send with Ctrl/Cmd+Enter
            shouldSendMessage = true;
          } else {
            // Plain Enter
            if (!hasLineBreaks) {
              // Send on single line
              shouldSendMessage = true;
            }
            // Otherwise, default behavior (new line on multi-line)
          }

          if (shouldSendMessage) {
            e.preventDefault();
            onEnterKeyPress?.(e);
          }
        }

        // Always pass the event to the parent's onKeyDown for other purposes (like tutorials)
        onKeyDown?.(e);
      },
      [onEnterKeyPress, onKeyDown],
    );

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange?.(e);
        // Adjust height after change
        adjustHeight();
      },
      [onChange, adjustHeight],
    );

    // Also adjust height if the value is changed programmatically
    React.useEffect(() => {
      adjustHeight();
    }, [value, adjustHeight]);

    return (
      <textarea
        ref={textareaRef}
        className={clsx(
          'flex min-h-9 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none overflow-y-hidden',
          className,
        )}
        rows={1}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        {...props}
      />
    );
  },
);
AutoTextarea.displayName = 'AutoTextarea';

export { AutoTextarea };

```
<!-- end of file -->

<!-- file -->
app/components/chat/chat-header.tsx
```xml
import * as React from 'react';
import { Plus, Settings, ArrowLeft } from 'lucide-react';
import { Button } from '~/components/ui/button';

export function ChatHeader() {
  return (
    <header className="h-14 border-b border-border px-6 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-semibold text-sm">Chat IA</h1>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Plus className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
}

```
<!-- end of file -->

<!-- file -->
app/components/chat/chat-input.tsx
```xml
import * as React from 'react';
import { Send } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { AutoTextarea, type AutoTextareaRef } from '~/components/auto-textarea';
import { cn } from '~/lib/utils';

const HINT_CHAR_LIMIT = 45;

interface ChatInputProps {
  inputValue: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleEnterKeyPress: () => void;
  handleSendMessage: () => void;
  textareaRef: React.Ref<AutoTextareaRef>;
  isSmallScreen: boolean;
  isAiTyping: boolean;
}

export function ChatInput({
  inputValue,
  handleInputChange,
  handleEnterKeyPress,
  handleSendMessage,
  textareaRef,
  isSmallScreen,
  isAiTyping,
}: ChatInputProps) {
  const [discoveredShortcuts, setDiscoveredShortcuts] = React.useState({
    singleLine: false,
    multiLine: false,
  });

  // Effect to load shortcut discovery state from sessionStorage
  React.useEffect(() => {
    const singleLine =
      sessionStorage.getItem('singleLineShortcutDiscovered') === 'true';
    const multiLine =
      sessionStorage.getItem('multiLineShortcutDiscovered') === 'true';
    setDiscoveredShortcuts({ singleLine, multiLine });
  }, []);

  // This handler is ONLY for tracking when to hide the hints.
  // It should not prevent default or trigger a send.
  const handleTutorialDiscovery = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter') {
      const isMultiLine = inputValue.includes('\n');

      // Condition for completing single-line tutorial:
      // Plain Enter on a single line.
      if (!isMultiLine && !e.shiftKey && !e.metaKey && !e.ctrlKey) {
        if (!discoveredShortcuts.singleLine) {
          sessionStorage.setItem('singleLineShortcutDiscovered', 'true');
          setDiscoveredShortcuts((prev) => ({ ...prev, singleLine: true }));
        }
      }

      // Condition for completing multi-line tutorial:
      // Ctrl+Enter on a multi-line message.
      if (isMultiLine && (e.metaKey || e.ctrlKey)) {
        if (!discoveredShortcuts.multiLine) {
          sessionStorage.setItem('multiLineShortcutDiscovered', 'true');
          setDiscoveredShortcuts((prev) => ({ ...prev, multiLine: true }));
        }
      }
    }
  };

  const isTextareaExpanded = React.useMemo(
    () => inputValue.split('\n').length > 1 || inputValue.length > 50,
    [inputValue],
  );

  const isLastLineTooLong =
    (inputValue.split('\n').pop()?.length || 0) > HINT_CHAR_LIMIT;

  return (
    <div
      className={cn(
        'min-h-16 relative transition-all duration-200 border-t',
        isTextareaExpanded
          ? 'bg-background shadow-lg shadow-black/5 border-transparent'
          : 'border-border',
      )}
    >
      {isTextareaExpanded && (
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent opacity-30" />
      )}
      <div className="mx-auto max-w-4xl px-5 h-full flex items-end pb-3">
        <div className="flex items-end gap-3 w-full">
          {/* Avatar placeholder to align with messages */}
          <div className="h-8 w-8 shrink-0" />
          <div className="flex-1 flex gap-2 items-end w-full">
            <div className="relative flex-1">
              <AutoTextarea
                ref={textareaRef}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleTutorialDiscovery}
                onEnterKeyPress={handleEnterKeyPress}
                placeholder="Digite sua pergunta sobre FUVEST..."
                className={cn(
                  'flex-1 max-h-40 border-input focus:border-ring focus:ring-1 focus:ring-ring focus:ring-offset-0 focus:ring-inset',
                  !isSmallScreen && 'pr-10',
                )}
              />
              {inputValue.length > 0 &&
                !isLastLineTooLong &&
                !isSmallScreen &&
                !isAiTyping && (
                  <div className="absolute bottom-2 right-2 flex items-center gap-2 text-xs text-muted-foreground">
                    {inputValue.includes('\n') ? (
                      !discoveredShortcuts.multiLine && (
                        <>
                          <span className="bg-muted px-1.5 py-0.5 rounded">
                            Enter para nova linha
                          </span>
                          <span className="bg-muted px-1.5 py-0.5 rounded">
                            Ctrl+Enter para enviar
                          </span>
                        </>
                      )
                    ) : !discoveredShortcuts.singleLine ? (
                      <>
                        <span className="bg-muted px-1.5 py-0.5 rounded">
                          Shift+Enter para nova linha
                        </span>
                        <span className="bg-muted px-1.5 py-0.5 rounded">
                          Enter para enviar
                        </span>
                      </>
                    ) : null}
                  </div>
                )}
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isAiTyping}
              className={cn(
                'h-9 w-9 p-2 shrink-0 rounded-lg transition-all duration-200',
                inputValue.trim() && !isAiTyping
                  ? 'bg-primary text-primary-foreground shadow-md hover:shadow-lg hover:bg-primary/90'
                  : 'bg-muted text-muted-foreground cursor-not-allowed',
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/components/chat/chat-sidebar.tsx
```xml
import {
  MessageCircle,
  HelpCircle,
  BookOpen,
  TrendingUp,
  Plus,
} from 'lucide-react';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '~/components/ui/dropdown-menu';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';
import { formatSidebarDate } from '~/lib/date-utils';
import { useAuth } from '~/auth/useAuth';
import type { Thread } from '~/types/chat';
import { Link } from 'react-router';
import { useChatActions } from '~/hooks/useChatActions';

interface ChatSidebarProps {
  threads: Thread[];
  currentThreadId: string | null;
  onNewThread: () => void;
  unsentMessage: string;
  unreadThreads: string[];
}

const navigationItems = [
  {
    title: 'Chat IA',
    icon: MessageCircle,
    url: '#',
    isActive: true,
  },
  {
    title: 'Questões',
    icon: HelpCircle,
    url: '#',
  },
  {
    title: 'Simulados',
    icon: BookOpen,
    url: '#',
  },
  {
    title: 'Progresso',
    icon: TrendingUp,
    url: '#',
  },
];

export function ChatSidebar({
  threads,
  currentThreadId,
  onNewThread,
  unsentMessage,
  unreadThreads,
}: ChatSidebarProps) {
  const { isAuthenticated, user, login, logout, isLoading } = useAuth();
  const { prefetchThread } = useChatActions();

  return (
    <div className="w-64 border-r border-border bg-background flex-col hidden lg:flex">
      {/* Sidebar Header */}
      <div className="h-14 border-b border-border px-4 flex items-center">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded bg-primary text-primary-foreground font-bold text-sm">
            F
          </div>
          <span className="font-semibold text-sm">FUVEST AI</span>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 px-2 py-2">
        <div className="space-y-1">
          {navigationItems.map((item) => (
            <a
              key={item.title}
              href={item.url}
              className={cn(
                'flex items-center gap-3 h-9 w-full px-3 rounded-md text-sm transition-colors',
                item.isActive
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-accent text-foreground',
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </a>
          ))}
        </div>

        {/* Thread List */}
        <div className="mt-4">
          <div className="flex items-center justify-between px-3 mb-2">
            <h3 className="text-sm font-semibold text-foreground">Conversas</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={onNewThread}
              className="h-6 w-6 p-0"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="space-y-1 overflow-y-scroll max-h-98">
            {(threads || []).map((thread) => (
              <Link
                key={thread.id}
                to={`/c/${thread.id}`}
                onMouseEnter={() => prefetchThread(thread.id)}
                className={cn(
                  'w-full text-left px-3 py-2 rounded-md text-sm transition-colors flex items-center gap-2',
                  currentThreadId === thread.id
                    ? 'bg-accent text-accent-foreground'
                    : 'hover:bg-accent text-foreground',
                )}
              >
                <div className="flex-1 truncate">
                  <div className="truncate">
                    {thread.title || 'Nova conversa'}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatSidebarDate(thread.updatedAt)}
                  </div>
                </div>
                {unreadThreads.includes(thread.id) && (
                  <span className="h-2 w-2 shrink-0 block rounded-full bg-primary" />
                )}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Sidebar Footer */}
      <div className="h-16 border-t border-border px-4 flex items-center">
        {isLoading ? (
          <Skeleton className="h-8 w-full" />
        ) : isAuthenticated && user ? (
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center gap-2 w-full text-left p-1 rounded-md hover:bg-accent">
              <Avatar className="h-8 w-8">
                {user.picture && <img src={user.picture} alt={user.name} />}
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1 truncate">
                <p className="text-sm font-semibold truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuItem>Minha Conta</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} variant="destructive">
                Sair
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button onClick={() => login(unsentMessage)} className="w-full">
            Fazer Login com Google
          </Button>
        )}
      </div>
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/components/chat/markdown-renderer.tsx
```xml
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';

interface MarkdownRendererProps {
  content: string;
}

/**
 * A component that renders markdown content, including LaTeX math expressions.
 * It leverages react-markdown with remark-math and rehype-katex.
 * This component is streaming-aware; incomplete markdown or LaTeX will be
 * rendered as plain text until it becomes valid.
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath]}
      rehypePlugins={[rehypeKatex]}
      components={{
        p: ({ node, ...props }) => <p className="mb-2 last:mb-0" {...props} />,
        strong: ({ node, ...props }) => (
          <strong className="font-semibold" {...props} />
        ),
        a: ({ node, ...props }) => (
          <a
            className="text-primary hover:underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul className="list-disc list-outside my-2 pl-6" {...props} />
        ),
        ol: ({ node, ...props }) => (
          <ol className="list-decimal list-outside my-2 pl-6" {...props} />
        ),
        li: ({ node, ...props }) => <li className="mb-1" {...props} />,
        code: ({ node, className, children, ...props }) => {
          // Check if the className contains a language definition (e.g., "language-js").
          // This is a reliable way to distinguish fenced code blocks from inline code.
          const match = /language-(\w+)/.exec(className || '');

          return !match ? (
            // Render INLINE code
            <code
              className="bg-muted text-muted-foreground rounded-sm px-1.5 py-1 font-mono text-sm"
              {...props}
            >
              {children}
            </code>
          ) : (
            // Render BLOCK code
            <div className="bg-gray-900 text-white my-2 rounded-md">
              <div className="text-xs text-muted-foreground px-3 pt-2 capitalize flex justify-between items-center">
                <span>{match[1]}</span>
                <button
                  onClick={() =>
                    navigator.clipboard.writeText(
                      String(children).replace(/\n$/, ''),
                    )
                  }
                  className="text-xs hover:text-white"
                >
                  Copy
                </button>
              </div>
              <pre className="p-3 overflow-x-auto">
                <code className="text-sm font-mono" {...props}>
                  {children}
                </code>
              </pre>
            </div>
          );
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default React.memo(MarkdownRenderer);

```
<!-- end of file -->

<!-- file -->
app/components/chat/message-list.tsx
~~~xml
import * as React from 'react';
import {
  Settings,
  Copy,
  Edit3,
  Trash2,
  Check,
  X,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Button } from '~/components/ui/button';
import { AutoTextarea } from '../auto-textarea';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';
import { formatMessageDate } from '~/lib/date-utils';
import type { ChatMessage } from '~/types/chat.ts';
import { useChatActions } from '~/hooks/useChatActions';
import MarkdownRenderer from './markdown-renderer';

// Define the actions that MessageComponent will need
interface MessageActions {
  saveEdit: (messageId: string, newContent: string) => void;
  copyMessage: (content: string) => void;
  deleteMessage: (messageId: string) => void;
  regenerateResponse: (aiMessageId: string) => void;
  navigateSibling: (messageId: string, direction: 'next' | 'prev') => void;
}

interface MessageComponentProps {
  message: ChatMessage;
  actions: MessageActions; // Actions are now passed as a prop
  isLastMessage?: boolean;
  isStreaming?: boolean;
}

const MessageComponent = React.memo(function MessageComponent({
  message,
  actions, // Destructure actions from props
  isLastMessage = false,
  isStreaming = false,
}: MessageComponentProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState(message.content);

  const handleSave = () => {
    // Only save if content has actually changed
    if (editValue.trim() !== message.content.trim()) {
      actions.saveEdit(message.id, editValue);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(message.content);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  const isDeleted = !!message.deletedAt;
  const isAI = message.role === 'ASSISTANT';

  return (
    <div
      id={message.id}
      className={cn(
        'w-full border-b border-border group transition-colors',
        isDeleted
          ? 'bg-destructive/10 hover:bg-destructive/12'
          : isAI
            ? 'bg-muted hover:bg-border'
            : 'bg-background hover:bg-muted',
      )}
    >
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3 items-start">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback
              className={cn(
                'text-xs font-medium',
                isAI
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground',
              )}
            >
              {isAI ? 'AI' : 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {isAI ? 'FUVEST AI' : 'Você'}
                </span>
                {isAI && message.model && (
                  <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                    {message.model}
                  </span>
                )}
              </div>
              {message.siblingCount > 1 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition <= 1}
                    onClick={() => actions.navigateSibling(message.id, 'prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span>
                    {message.siblingPosition}/{message.siblingCount}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition >= message.siblingCount}
                    onClick={() => actions.navigateSibling(message.id, 'next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex-grow">
                {isEditing ? (
                  <div className="space-y-2">
                    <AutoTextarea
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      onKeyDown={handleEditKeyDown}
                      className="flex-1 min-h-[60px] text-sm"
                    />
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" onClick={handleSave}>
                        <Check className="h-3 w-3 mr-1" />
                        Salvar
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Cancelar
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div
                    className={cn(
                      'relative text-sm leading-relaxed text-foreground pr-11 break-words [hyphens:auto]',
                      isDeleted && 'text-muted-foreground italic',
                    )}
                  >
                    {isDeleted ? (
                      'This message was deleted on this branch.'
                    ) : (
                      <MarkdownRenderer content={message.content} />
                    )}
                    {/* Blinking cursor for streaming messages */}
                    {isStreaming && !isDeleted && (
                      <span className="animate-pulse">|</span>
                    )}
                  </div>
                )}
              </div>

              <div
                className={cn(
                  'sticky message-footer bottom-0 z-10 flex items-center justify-between pt-2 transition-colors',
                  isDeleted
                    ? 'bg-transparent'
                    : isAI
                      ? 'bg-muted group-hover:bg-border'
                      : 'bg-background group-hover:bg-muted',
                )}
              >
                <span className="text-xs text-muted-foreground">
                  {formatMessageDate(message.createdAt)}
                </span>
                {!isDeleted && (
                  <div
                    className={cn(
                      'flex items-center gap-1 transition-opacity duration-200',
                      isLastMessage
                        ? 'opacity-100'
                        : 'opacity-0 group-hover:opacity-100',
                    )}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => actions.copyMessage(message.content)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    {isAI && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => actions.regenerateResponse(message.id)}
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Settings className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleEdit}>
                          <Edit3 className="h-3 w-3 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => actions.deleteMessage(message.id)}
                          variant="destructive"
                        >
                          <Trash2 className="h-3 w-3 mr-2" />
                          Excluir branch
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

const SkeletonMessage = React.memo(function SkeletonMessage() {
  return (
    <div className="w-full border-b border-border bg-muted">
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback className="bg-primary/20 text-primary-foreground text-xs font-medium">
              AI
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-muted-foreground">
                FUVEST AI
              </span>
            </div>
            <div className="space-y-2 pr-11">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

interface MessageListProps {
  messages: ChatMessage[];
  streamingResponse: ChatMessage | null;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
}

export function MessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  // Call useChatActions() ONCE here in the parent component
  const chatActions = useChatActions();

  return (
    <div className="flex-1 overflow-y-scroll">
      <div className="w-full">
        {messages.map((message, index) => (
          <MessageComponent
            key={`${message.id}-${message.siblingPosition}`}
            message={message}
            isLastMessage={index === messages.length - 1 && !streamingResponse}
            isStreaming={false}
            actions={chatActions} // Pass the actions object down as a prop
          />
        ))}
        {streamingResponse && (
          <>
            {streamingResponse.content.length === 0 ? (
              <SkeletonMessage />
            ) : (
              <MessageComponent
                message={streamingResponse}
                isLastMessage={false}
                isStreaming={true}
                actions={chatActions} // Also pass to the streaming message component
              />
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}

// Virtualized version of MessageList for better performance with large message lists
// Smart message list that automatically chooses between regular and virtualized rendering
export function SmartMessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  // Use virtualization only for larger message lists (50+ messages)
  // This avoids unnecessary overhead for small conversations
  const shouldVirtualize = messages.length >= 50;

  if (shouldVirtualize) {
    return (
      <VirtualizedMessageList
        messages={messages}
        streamingResponse={streamingResponse}
        messagesEndRef={messagesEndRef}
      />
    );
  }

  return (
    <MessageList
      messages={messages}
      streamingResponse={streamingResponse}
      messagesEndRef={messagesEndRef}
    />
  );
}

export function VirtualizedMessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  const chatActions = useChatActions();
  const parentRef = React.useRef<HTMLDivElement>(null);

  // Combine messages with streaming response for virtualization
  const allItems = React.useMemo(() => {
    const items = [...messages];
    if (streamingResponse) {
      items.push(streamingResponse);
    }
    return items;
  }, [messages, streamingResponse]);

  // Dynamic height estimation based on message content
  const estimateSize = React.useCallback(
    (index: number) => {
      const message = allItems[index];
      if (!message) return 120;

      // Base height for message structure (avatar, padding, etc.)
      let estimatedHeight = 80;

      // Estimate additional height based on content length
      const contentLength = message.content.length;
      const estimatedLines = Math.max(1, Math.ceil(contentLength / 80)); // ~80 chars per line
      estimatedHeight += estimatedLines * 24; // ~24px per line

      // Add extra height for code blocks and math expressions
      const codeBlockMatches = message.content.match(/```[\s\S]*?```/g) || [];
      const mathBlockMatches = message.content.match(/\$\$[\s\S]*?\$\$/g) || [];
      estimatedHeight +=
        codeBlockMatches.length * 100 + mathBlockMatches.length * 60;

      // Add height for lists and other markdown elements
      const listMatches = message.content.match(/^[\s]*[-*+]\s/gm) || [];
      estimatedHeight += listMatches.length * 28;

      return Math.max(120, estimatedHeight);
    },
    [allItems],
  );

  // Create virtualizer with dynamic sizing
  const virtualizer = useVirtualizer({
    count: allItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 8, // Render 8 extra items above/below visible area for smooth scrolling
    measureElement: (element) => {
      // Use the actual measured height when available
      return element?.getBoundingClientRect().height ?? estimateSize(0);
    },
    // Enable smooth scrolling optimizations
    scrollMargin: parentRef.current?.offsetTop ?? 0,
    gap: 0, // No gap between items since we handle spacing in CSS
  });

  // Track if user has manually scrolled up
  const [shouldAutoScroll, setShouldAutoScroll] = React.useState(true);
  const isInitialLoad = React.useRef(true);

  // Handle scroll events to detect user scrolling
  React.useEffect(() => {
    const scrollElement = parentRef.current;
    if (!scrollElement) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold

      setShouldAutoScroll(isAtBottom);
    };

    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-scroll to bottom when new messages arrive (only if user hasn't scrolled up)
  React.useEffect(() => {
    if (allItems.length > 0 && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      const behavior = isInitialLoad.current ? 'auto' : 'smooth';

      virtualizer.scrollToIndex(lastIndex, {
        align: 'end',
        behavior,
      });

      if (isInitialLoad.current) {
        isInitialLoad.current = false;
      }
    }
  }, [allItems.length, virtualizer, shouldAutoScroll]);

  // Auto-scroll during streaming (content updates)
  React.useEffect(() => {
    if (streamingResponse && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      virtualizer.scrollToIndex(lastIndex, {
        align: 'end',
        behavior: 'auto', // Use auto for streaming to avoid jank
      });
    }
  }, [
    streamingResponse?.content,
    virtualizer,
    shouldAutoScroll,
    allItems.length,
  ]);

  return (
    <div ref={parentRef} className="flex-1 overflow-y-scroll">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const message = allItems[virtualItem.index];
          const isLastMessage =
            virtualItem.index === allItems.length - 1 && !streamingResponse;
          const isStreamingMessage = !!(
            streamingResponse && message.id === streamingResponse.id
          );

          return (
            <div
              key={`${message.id}-${message.siblingPosition}`}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
              ref={virtualizer.measureElement}
              data-index={virtualItem.index}
            >
              {isStreamingMessage && streamingResponse?.content.length === 0 ? (
                <SkeletonMessage />
              ) : (
                <MessageComponent
                  message={message}
                  isLastMessage={isLastMessage}
                  isStreaming={isStreamingMessage}
                  actions={chatActions}
                />
              )}
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}

~~~
<!-- end of file -->

<!-- file -->
app/components/chat-interface.tsx
```xml
import { useRef, useEffect, useLayoutEffect } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useParams } from 'react-router';

import { ChatSidebar } from '~/components/chat/chat-sidebar';
import { ChatHeader } from '~/components/chat/chat-header';
import { SmartMessageList } from '~/components/chat/message-list';
import { ChatInput } from '~/components/chat/chat-input';
import { useChatActions } from '~/hooks/useChatActions';
import { useChatForm } from '~/hooks/useChatForm';
import { useMediaQuery } from '~/hooks/useMediaQuery';
import { useChatStore } from '~/store/chatStore';
import { MainChatSkeleton } from '~/components/chat-skeleton';
import * as chatService from '~/services/chat-service';
import { useAuth } from '~/auth/useAuth';

const EMPTY_MESSAGES: [] = [];

export function ChatInterface() {
  const { sendMessage, createNewThread } = useChatActions();
  const { threadId: urlThreadId } = useParams<{ threadId?: string }>();
  const { isAuthenticated } = useAuth();

  // Actions and static data can be pulled non-reactively or with stable selectors
  const {
    setMessagesForThread,
    setCurrentThreadId,
    setThreads,
    setThreadsStatus,
    removeUnreadThread,
  } = useChatStore.getState();
  const { threads, threadsStatus, unreadThreads } = useChatStore(
    useShallow((state) => ({
      threads: state.threads,
      threadsStatus: state.threadsStatus,
      unreadThreads: state.unreadThreads,
    })),
  );

  // --- Effect to fetch threads list ---
  useEffect(() => {
    // Only fetch if authenticated and threads haven't been loaded/are not loading
    if (isAuthenticated && threadsStatus === 'idle') {
      setThreadsStatus('loading');
      chatService
        .fetchThreads()
        .then((fetchedThreads) => {
          setThreads(fetchedThreads);
          setThreadsStatus('success');
        })
        .catch((err) => {
          console.error('Failed to fetch threads:', err);
          setThreadsStatus('error');
        });
    }
  }, [isAuthenticated, threadsStatus, setThreads, setThreadsStatus]);

  // --- Smart Data Fetching & State Sync ---
  // This effect synchronizes the URL, fetches data, and marks threads as read.
  useEffect(() => {
    const currentId = urlThreadId || null;
    setCurrentThreadId(currentId);

    // If we've navigated to a thread, mark it as read.
    if (currentId) {
      removeUnreadThread(currentId);
    }

    // Fetch messages only if a thread is selected and its messages are truly missing.
    if (
      currentId &&
      useChatStore.getState().messagesByThread[currentId] === undefined
    ) {
      // Set to an empty array immediately to mark as 'loading' and prevent re-fetches.
      setMessagesForThread(currentId, []);
      chatService
        .fetchMessages(currentId)
        .then((messages) => setMessagesForThread(currentId, messages))
        .catch((err) => {
          console.error(
            `Failed to fetch messages for thread ${currentId}:`,
            err,
          );
          // TODO: Handle UI error state, maybe remove the thread entry or show toast.
        });
    }
  }, [
    urlThreadId,
    setCurrentThreadId,
    setMessagesForThread,
    removeUnreadThread,
  ]);

  // --- Optimized Data Subscription ---
  // This selector is now highly optimized. It only subscribes the component to
  // changes for the currently active thread, preventing re-renders from prefetching.
  const { messages, streamingResponse, isAiTyping, currentThreadId } =
    useChatStore(
      useShallow((state) => {
        const id = state.currentThreadId;
        const msgs = id ? state.messagesByThread[id] : EMPTY_MESSAGES;
        const stream = id ? state.streamingResponses[id] : null;
        return {
          messages: msgs === undefined ? EMPTY_MESSAGES : msgs, // Handle loading case
          streamingResponse: stream,
          isAiTyping: !!stream,
          currentThreadId: id,
        };
      }),
    );

  const {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef,
  } = useChatForm({
    onSubmit: sendMessage,
    isAiTyping,
  });

  const isSmallScreen = useMediaQuery('(max-width: 1023px)');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isInitialLoad = useRef(true);

  useEffect(() => {
    isInitialLoad.current = true;
  }, [currentThreadId]);

  useLayoutEffect(() => {
    const shouldScroll = messages.length > 0 || streamingResponse;
    if (!shouldScroll) return;

    const isStreaming = !!streamingResponse?.content;
    const behavior = isInitialLoad.current || isStreaming ? 'auto' : 'smooth';

    messagesEndRef.current?.scrollIntoView({ behavior, block: 'end' });

    if (isInitialLoad.current) {
      isInitialLoad.current = false;
    }
  }, [messages, streamingResponse?.content]);

  // A thread is loading if the URL says we're on it, but its message array
  // in the store is `undefined` (meaning we haven't started fetching for it yet).
  const isLoading = urlThreadId
    ? useChatStore.getState().messagesByThread[urlThreadId] === undefined
    : false;

  return (
    <div className="flex h-screen bg-background">
      <ChatSidebar
        threads={threads}
        currentThreadId={currentThreadId}
        onNewThread={createNewThread}
        unsentMessage={inputValue}
        unreadThreads={unreadThreads}
      />
      {isLoading ? (
        <MainChatSkeleton />
      ) : (
        <div className="flex-1 flex flex-col">
          <ChatHeader />
          <SmartMessageList
            messages={messages}
            streamingResponse={streamingResponse}
            messagesEndRef={messagesEndRef}
          />
          <ChatInput
            inputValue={inputValue}
            handleInputChange={handleInputChange}
            handleEnterKeyPress={handleEnterKeyPress}
            handleSendMessage={handleSendMessageClick}
            textareaRef={textareaRef}
            isSmallScreen={isSmallScreen}
            isAiTyping={isAiTyping}
          />
        </div>
      )}
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/components/chat-skeleton.tsx
```xml
import { Skeleton } from '~/components/ui/skeleton';

export function MainChatSkeleton() {
  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="h-14 border-b border-border px-6 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-4 w-20" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
      {/* Message List */}
      <div className="flex-1 p-6 space-y-8">
        {/* AI message skeleton */}
        <div className="flex gap-3">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
          </div>
        </div>
        {/* User message skeleton */}
        <div className="flex gap-3 items-end flex-row-reverse">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24 self-end" />
            <Skeleton className="h-4 w-2/5 self-end" />
          </div>
        </div>
        {/* AI message skeleton */}
        <div className="flex gap-3">
          <Skeleton className="h-8 w-8 rounded-full shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/6" />
          </div>
        </div>
      </div>
      {/* Input */}
      <div className="min-h-16 border-t border-border">
        <div className="mx-auto max-w-4xl p-3 flex items-end gap-3">
          <div className="h-8 w-8 shrink-0" />
          <div className="flex-1 flex gap-2 items-end w-full">
            <Skeleton className="h-9 w-full rounded-md" />
            <Skeleton className="h-9 w-9 rounded-lg" />
          </div>
        </div>
      </div>
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/components/sonner.tsx
```xml
import { Toaster as Sonner } from 'sonner';

export const Toaster = () => {
  return <Sonner theme="light" position="top-right" richColors closeButton />;
};

```
<!-- end of file -->

<!-- file -->
app/components/ui/avatar.tsx
```xml
'use client';

import * as React from 'react';
import * as AvatarPrimitive from '@radix-ui/react-avatar';

import { cn } from '~/lib/utils';

function Avatar({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Root>) {
  return (
    <AvatarPrimitive.Root
      data-slot="avatar"
      className={cn(
        'relative flex size-8 shrink-0 overflow-hidden rounded-full',
        className,
      )}
      {...props}
    />
  );
}

function AvatarImage({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Image>) {
  return (
    <AvatarPrimitive.Image
      data-slot="avatar-image"
      className={cn('aspect-square size-full', className)}
      {...props}
    />
  );
}

function AvatarFallback({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {
  return (
    <AvatarPrimitive.Fallback
      data-slot="avatar-fallback"
      className={cn(
        'bg-muted flex size-full items-center justify-center rounded-full',
        className,
      )}
      {...props}
    />
  );
}

export { Avatar, AvatarImage, AvatarFallback };

```
<!-- end of file -->

<!-- file -->
app/components/ui/button.tsx
```xml
import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '~/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };

```
<!-- end of file -->

<!-- file -->
app/components/ui/dropdown-menu.tsx
```xml
import * as React from 'react';
import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';

import { cn } from '~/lib/utils';

function DropdownMenu({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {
  return <DropdownMenuPrimitive.Root data-slot="dropdown-menu" {...props} />;
}

function DropdownMenuPortal({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {
  return (
    <DropdownMenuPrimitive.Portal data-slot="dropdown-menu-portal" {...props} />
  );
}

function DropdownMenuTrigger({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {
  return (
    <DropdownMenuPrimitive.Trigger
      data-slot="dropdown-menu-trigger"
      {...props}
    />
  );
}

function DropdownMenuContent({
  className,
  sideOffset = 4,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {
  return (
    <DropdownMenuPrimitive.Portal>
      <DropdownMenuPrimitive.Content
        data-slot="dropdown-menu-content"
        sideOffset={sideOffset}
        className={cn(
          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md',
          className,
        )}
        {...props}
      />
    </DropdownMenuPrimitive.Portal>
  );
}

function DropdownMenuGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {
  return (
    <DropdownMenuPrimitive.Group data-slot="dropdown-menu-group" {...props} />
  );
}

function DropdownMenuItem({
  className,
  inset,
  variant = 'default',
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {
  inset?: boolean;
  variant?: 'default' | 'destructive';
}) {
  return (
    <DropdownMenuPrimitive.Item
      data-slot="dropdown-menu-item"
      data-inset={inset}
      data-variant={variant}
      className={cn(
        "focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      {...props}
    />
  );
}

function DropdownMenuCheckboxItem({
  className,
  children,
  checked,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {
  return (
    <DropdownMenuPrimitive.CheckboxItem
      data-slot="dropdown-menu-checkbox-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      checked={checked}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.CheckboxItem>
  );
}

function DropdownMenuRadioGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {
  return (
    <DropdownMenuPrimitive.RadioGroup
      data-slot="dropdown-menu-radio-group"
      {...props}
    />
  );
}

function DropdownMenuRadioItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {
  return (
    <DropdownMenuPrimitive.RadioItem
      data-slot="dropdown-menu-radio-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CircleIcon className="size-2 fill-current" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.RadioItem>
  );
}

function DropdownMenuLabel({
  className,
  inset,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {
  inset?: boolean;
}) {
  return (
    <DropdownMenuPrimitive.Label
      data-slot="dropdown-menu-label"
      data-inset={inset}
      className={cn(
        'px-2 py-1.5 text-sm font-medium data-[inset]:pl-8',
        className,
      )}
      {...props}
    />
  );
}

function DropdownMenuSeparator({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {
  return (
    <DropdownMenuPrimitive.Separator
      data-slot="dropdown-menu-separator"
      className={cn('bg-border -mx-1 my-1 h-px', className)}
      {...props}
    />
  );
}

function DropdownMenuShortcut({
  className,
  ...props
}: React.ComponentProps<'span'>) {
  return (
    <span
      data-slot="dropdown-menu-shortcut"
      className={cn(
        'text-muted-foreground ml-auto text-xs tracking-widest',
        className,
      )}
      {...props}
    />
  );
}

function DropdownMenuSub({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {
  return <DropdownMenuPrimitive.Sub data-slot="dropdown-menu-sub" {...props} />;
}

function DropdownMenuSubTrigger({
  className,
  inset,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {
  inset?: boolean;
}) {
  return (
    <DropdownMenuPrimitive.SubTrigger
      data-slot="dropdown-menu-sub-trigger"
      data-inset={inset}
      className={cn(
        'focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8',
        className,
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="ml-auto size-4" />
    </DropdownMenuPrimitive.SubTrigger>
  );
}

function DropdownMenuSubContent({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {
  return (
    <DropdownMenuPrimitive.SubContent
      data-slot="dropdown-menu-sub-content"
      className={cn(
        'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg',
        className,
      )}
      {...props}
    />
  );
}

export {
  DropdownMenu,
  DropdownMenuPortal,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
};

```
<!-- end of file -->

<!-- file -->
app/components/ui/input.tsx
```xml
import * as React from 'react';

import { cn } from '~/lib/utils';

function Input({ className, type, ...props }: React.ComponentProps<'input'>) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
        className,
      )}
      {...props}
    />
  );
}

export { Input };

```
<!-- end of file -->

<!-- file -->
app/components/ui/skeleton.tsx
```xml
import { cn } from '~/lib/utils';

function Skeleton({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="skeleton"
      className={cn('bg-accent animate-pulse rounded-md', className)}
      {...props}
    />
  );
}

export { Skeleton };

```
<!-- end of file -->

<!-- file -->
app/hooks/useApi.ts
```xml
import { useState, useCallback } from 'react';
import { AxiosError } from 'axios';
import { apiClient } from '~/utils/axios-client';

export function useApi() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const get = useCallback(async <T>(url: string): Promise<T | null> => {
    setLoading(true);
    setError(null);
    try {
      const data = await apiClient.get<T>(url);
      return data;
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        setError(err.response?.data?.message || 'Request failed');
      } else {
        setError('Request failed');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const post = useCallback(
    async <T, U = Record<string, unknown>>(
      url: string,
      data?: U,
    ): Promise<T | null> => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.post<T>(url, data);
        return response;
      } catch (err: unknown) {
        if (err instanceof AxiosError) {
          setError(err.response?.data?.message || 'Request failed');
        } else {
          setError('Request failed');
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  const put = useCallback(
    async <T, U = Record<string, unknown>>(
      url: string,
      data?: U,
    ): Promise<T | null> => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.put<T>(url, data);
        return response;
      } catch (err: unknown) {
        if (err instanceof AxiosError) {
          setError(err.response?.data?.message || 'Request failed');
        } else {
          setError('Request failed');
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  const del = useCallback(async <T>(url: string): Promise<T | null> => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.delete<T>(url);
      return response;
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        setError(err.response?.data?.message || 'Request failed');
      } else {
        setError('Request failed');
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { get, post, put, delete: del, loading, error };
}

```
<!-- end of file -->

<!-- file -->
app/hooks/useChatActions.ts
```xml
import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';
import DOMPurify from 'dompurify';

import { useAuth } from '~/auth/useAuth';
import * as chatService from '~/services/chat-service';
import { useChatStore } from '~/store/chatStore';
import { useChatConnection } from '~/hooks/useChatConnection';

export function useChatActions() {
  const { isAuthenticated, login } = useAuth();
  const navigate = useNavigate();
  const { startStreamingResponse } = useChatConnection(navigate);

  const {
    addThread,
    updateThread,
    setMessagesForThread,
    setCurrentThreadId,
    addMessageToThread,
    replaceMessageInThread,
    removeMessageFromThread,
    abortStreaming,
  } = useChatStore.getState();

  const prefetchThread = useCallback(
    (threadId: string) => {
      const store = useChatStore.getState();
      // Only prefetch if we don't already have the messages for that thread.
      if (!store.messagesByThread[threadId]) {
        console.log(`Prefetching messages for thread: ${threadId}`);
        chatService
          .fetchMessages(threadId)
          .then((msgs) => setMessagesForThread(threadId, msgs))
          .catch((err) =>
            console.error(`Prefetch failed for ${threadId}`, err),
          );
      }
    },
    [setMessagesForThread],
  );

  const createNewThread = useCallback(() => {
    setCurrentThreadId(null);
    navigate('/');
  }, [setCurrentThreadId, navigate]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;
      if (!isAuthenticated) {
        toast.warning('Você precisa estar logado para enviar uma mensagem.', {
          description: 'Sua mensagem foi salva. Faça o login para continuar.',
          action: { label: 'Fazer Login', onClick: () => login(content) },
        });
        return;
      }
      const sanitizedInput = DOMPurify.sanitize(content);
      if (!sanitizedInput.trim()) return;
      const currentThreadId = useChatStore.getState().currentThreadId;
      const processSend = async (threadId: string, parentId: string | null) => {
        const tempUserMessageId = `temp-user-${Date.now()}`;
        addMessageToThread(threadId, {
          id: tempUserMessageId,
          content: sanitizedInput,
          role: 'USER',
          createdAt: new Date().toISOString(),
          threadId,
          parentId,
          siblingCount: 1,
          siblingPosition: 1,
        });
        try {
          const userMessage = await chatService.sendMessage(
            sanitizedInput,
            threadId,
            parentId,
            tempUserMessageId,
          );
          replaceMessageInThread(threadId, tempUserMessageId, userMessage);
          if (userMessage.threadTitle) {
            updateThread(threadId, userMessage.threadTitle);
          }
          startStreamingResponse(userMessage.id, userMessage.threadId);
        } catch {
          toast.error('Error sending message');
          abortStreaming(threadId);
          removeMessageFromThread(threadId, tempUserMessageId);
        }
      };
      if (currentThreadId) {
        const messages =
          useChatStore.getState().messagesByThread[currentThreadId] || [];
        const lastMessageId =
          messages.length > 0 ? messages[messages.length - 1].id : null;
        await processSend(currentThreadId, lastMessageId);
      } else {
        try {
          const newThread = await chatService.createThread(
            sanitizedInput.substring(0, 50),
          );
          addThread(newThread);
          navigate(`/c/${newThread.id}`);
          await processSend(newThread.id, null);
        } catch {
          toast.error('Failed to create new thread.');
        }
      }
    },
    [
      isAuthenticated,
      login,
      startStreamingResponse,
      addMessageToThread,
      replaceMessageInThread,
      removeMessageFromThread,
      abortStreaming,
      updateThread,
      addThread,
      navigate,
    ],
  );

  const saveEdit = useCallback(
    async (messageId: string, newContent: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!newContent.trim() || !currentThreadId) return;

      // Find the message being edited to check its role
      const messages =
        useChatStore.getState().messagesByThread[currentThreadId] || [];
      const messageToEdit = messages.find((m) => m.id === messageId);

      if (!messageToEdit) {
        toast.error('Cannot edit message: original not found.');
        return;
      }

      const sanitizedContent = DOMPurify.sanitize(newContent);
      try {
        const updatedMessages = await chatService.editMessage(
          messageId,
          sanitizedContent,
        );
        setMessagesForThread(currentThreadId, updatedMessages);

        // --- BUG FIX ---
        // Only trigger a new AI response if the edited message was a USER message.
        if (messageToEdit.role === 'USER') {
          toast.success('Branch created. Generating new AI response...');
          const lastMessage = updatedMessages[updatedMessages.length - 1];
          startStreamingResponse(lastMessage.id, currentThreadId);
        } else {
          toast.success('Assistant message updated.');
        }
      } catch (error) {
        toast.error('Failed to update message.');
      }
    },
    [setMessagesForThread, startStreamingResponse],
  );

  const regenerateResponse = useCallback(
    async (aiMessageId: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      const messages =
        useChatStore.getState().messagesByThread[currentThreadId] || [];
      const aiMessage = messages.find((m) => m.id === aiMessageId);
      if (!aiMessage || !aiMessage.parentId) {
        toast.error('Cannot regenerate: context not found.');
        return;
      }
      const parentIndex = messages.findIndex(
        (m) => m.id === aiMessage.parentId,
      );
      const newHistory = messages.slice(0, parentIndex + 1);
      setMessagesForThread(currentThreadId, newHistory);
      startStreamingResponse(aiMessage.parentId, currentThreadId, true);
    },
    [setMessagesForThread, startStreamingResponse],
  );

  const deleteMessage = useCallback(
    async (messageId: string) => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      try {
        const updatedMessages = await chatService.deleteMessage(messageId);
        setMessagesForThread(currentThreadId, updatedMessages);
        toast.info('Created a new branch with the message deleted.');
      } catch {
        toast.error('Failed to delete message.');
      }
    },
    [setMessagesForThread],
  );

  const navigateSibling = useCallback(
    async (messageId: string, direction: 'next' | 'prev') => {
      const currentThreadId = useChatStore.getState().currentThreadId;
      if (!currentThreadId) return;
      try {
        const messageData = await chatService.navigateSibling(
          messageId,
          direction,
        );
        setMessagesForThread(currentThreadId, messageData);
      } catch (error) {
        toast.error(`No ${direction} version available.`);
      }
    },
    [setMessagesForThread],
  );

  const copyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Copied to clipboard!');
  }, []);

  return {
    sendMessage,
    regenerateResponse,
    saveEdit,
    deleteMessage,
    navigateSibling,
    createNewThread,
    copyMessage,
    prefetchThread,
  };
}

```
<!-- end of file -->

<!-- file -->
app/hooks/useChatConnection.ts
```xml
import { useRef, useCallback, type MouseEvent } from 'react';
import { toast } from 'sonner';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import type { NavigateFunction } from 'react-router';
import { useChatStore } from '~/store/chatStore';
import type { ChatMessage } from '~/types/chat';

// Custom error classes to control retry logic
class FatalError extends Error {}

/**
 * Manages multiple, independent SSE connections for chat responses.
 * Each connection is tied to a specific threadId.
 */
export function useChatConnection(navigate: NavigateFunction) {
  // Use a ref to store a map of active AbortControllers, keyed by threadId.
  const controllersRef = useRef<Record<string, AbortController>>({});

  const {
    startStreaming,
    updateStreamingContent,
    finalizeStreaming,
    abortStreaming,
    getThreadById,
    addUnreadThread,
  } = useChatStore.getState();

  const startStreamingResponse = useCallback(
    async (parentId: string, threadId: string, isRegen = false) => {
      // If a controller for this thread already exists, abort the previous request.
      if (controllersRef.current[threadId]) {
        controllersRef.current[threadId].abort();
      }

      const ctrl = new AbortController();
      controllersRef.current[threadId] = ctrl;

      const streamUrl = isRegen
        ? `/api/chat/stream/${parentId}/regenerate`
        : `/api/chat/stream/${parentId}`;

      // Create a skeleton message and add it to the streaming state immediately.
      // This allows the UI to show a skeleton right away.
      const tempAiMessageId = `temp-ai-${Date.now()}`;
      startStreaming(threadId, {
        id: tempAiMessageId,
        content: '', // Start with empty content
        role: 'ASSISTANT',
        createdAt: new Date().toISOString(),
        threadId: threadId,
        parentId: parentId,
        siblingCount: 1,
        siblingPosition: 1,
      });

      await fetchEventSource(streamUrl, {
        signal: ctrl.signal,
        credentials: 'include',

        onopen: async (response) => {
          if (response.ok) return;
          if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            throw new FatalError(`Client Error: ${response.status}`);
          }
        },

        onmessage: (event) => {
          if (ctrl.signal.aborted || !event.data) return;
          const data = JSON.parse(event.data);

          if (data.isComplete && data.finalMessage) {
            const finalMessage = data.finalMessage as ChatMessage;
            finalizeStreaming(threadId, finalMessage);

            // Check if we should show a toast / unread indicator
            const currentThreadId = useChatStore.getState().currentThreadId;
            if (threadId !== currentThreadId) {
              addUnreadThread(threadId); // Mark thread as unread
              const thread = getThreadById(threadId);
              toast.success(`New message in "${thread?.title || 'a chat'}"`, {
                description: finalMessage.content.substring(0, 100) + '...',
                action: {
                  label: 'View',
                  onClick: (e: MouseEvent<HTMLButtonElement>) => {
                    const url = `/c/${threadId}`;
                    if (e.ctrlKey || e.metaKey) {
                      window.open(url, '_blank', 'noopener,noreferrer');
                    } else {
                      navigate(url);
                    }
                  },
                },
              });
            }
            ctrl.abort(); // End this connection
            return;
          }

          const { content, debug } = data;
          updateStreamingContent(threadId, content, debug);
        },

        onclose: () => {
          // If the connection closes unexpectedly, clean up the streaming state for that thread.
          abortStreaming(threadId);
          delete controllersRef.current[threadId];
        },

        onerror: (err) => {
          abortStreaming(threadId); // Clean up on error
          delete controllersRef.current[threadId];

          if (err instanceof FatalError) {
            toast.error('A fatal error occurred.', {
              description: 'Please check your connection or try again later.',
            });
            throw err; // Stop retrying
          }
          toast.warning('Connection issue. Retrying...');
          return 2000; // Retry delay
        },
      });
    },
    [
      startStreaming,
      updateStreamingContent,
      finalizeStreaming,
      abortStreaming,
      getThreadById,
      addUnreadThread,
      navigate,
    ],
  );

  return { startStreamingResponse };
}

```
<!-- end of file -->

<!-- file -->
app/hooks/useChatForm.ts
```xml
import { useRef, useCallback, useEffect } from 'react';
import type { ChangeEvent, Ref } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useChatStore } from '~/store/chatStore';
import type { AutoTextareaRef } from '~/components/auto-textarea';

interface UseChatFormProps {
  onSubmit: (value: string) => void;
  isAiTyping: boolean;
}

/**
 * Manages the state and logic of the chat input form, including
 * text input, enter key handling, and focusing.
 *
 * @param onSubmit - Callback function to execute when the form is submitted.
 * @param isAiTyping - Boolean to disable the form during AI responses.
 * @returns An object with form state and handlers.
 */
export function useChatForm({ onSubmit, isAiTyping }: UseChatFormProps) {
  const { inputValue, setInputValue } = useChatStore(
    useShallow((state) => ({
      inputValue: state.inputValue,
      setInputValue: state.setInputValue,
    })),
  );
  const textareaRef = useRef<AutoTextareaRef>(null);

  // Restore unsent message from localStorage on initial load
  useEffect(() => {
    const unsentMessage = localStorage.getItem('unsentChatMessage');
    if (unsentMessage) {
      setInputValue(unsentMessage);
      localStorage.removeItem('unsentChatMessage');
    }
  }, [setInputValue]);

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      setInputValue(e.target.value);
    },
    [setInputValue],
  );

  const handleEnterKeyPress = useCallback(() => {
    if (!inputValue.trim() || isAiTyping) return;
    onSubmit(inputValue);
    setInputValue('');
    textareaRef.current?.reset();
  }, [inputValue, isAiTyping, onSubmit, setInputValue]);

  const handleSendMessageClick = useCallback(() => {
    handleEnterKeyPress();
  }, [handleEnterKeyPress]);

  return {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef: textareaRef as Ref<AutoTextareaRef>, // Cast for external use
  };
}

```
<!-- end of file -->

<!-- file -->
app/hooks/useMediaQuery.ts
```xml
import * as React from 'react';

/**
 * A custom hook to efficiently track media query matches.
 * @param query The media query string to watch.
 * @returns `true` if the query matches, otherwise `false`.
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    // This effect runs only on the client
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(query);
      if (media.matches !== matches) {
        setMatches(media.matches);
      }
      const listener = () => setMatches(media.matches);
      // Use the modern `addEventListener` method
      media.addEventListener('change', listener);
      // Cleanup listener on component unmount
      return () => media.removeEventListener('change', listener);
    }
  }, [matches, query]);

  return matches;
}

```
<!-- end of file -->

<!-- file -->
app/lib/date-utils.ts
```xml
/**
 * Formats a date for display in the message list, localized for Brazilian Portuguese.
 * Provides user-friendly relative times like "Hoje às 10:30" or "Ontem às 15:00".
 *
 * @param date - The date to format, as a string or Date object.
 * @returns A formatted, localized date string.
 */
export function formatMessageDate(date: string | Date): string {
  const messageDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();

  const timeFormatter = new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
  );
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfToday.getDate() - 1);

  // Use a rolling 7-day window from the start of today
  const sevenDaysAgo = new Date(startOfToday);
  sevenDaysAgo.setDate(startOfToday.getDate() - 6);

  const timePart = timeFormatter.format(messageDate);

  // Check if the message was sent in the last 60 seconds
  const diffInSeconds = (now.getTime() - messageDate.getTime()) / 1000;
  if (diffInSeconds < 60) {
    return 'agora mesmo';
  }

  // Check if the date is today
  if (messageDate >= startOfToday) {
    return `Hoje às ${timePart}`;
  }

  // Check if the date was yesterday
  if (messageDate >= startOfYesterday) {
    return `Ontem às ${timePart}`;
  }

  // Check if the date was in the last 7 days
  if (messageDate >= sevenDaysAgo) {
    const weekdayFormatter = new Intl.DateTimeFormat('pt-BR', {
      weekday: 'long',
    });
    const weekday = weekdayFormatter.format(messageDate);
    // Capitalize the first letter
    const capitalizedWeekday =
      weekday.charAt(0).toUpperCase() + weekday.slice(1);
    return `${capitalizedWeekday} às ${timePart}`;
  }

  // For dates older than a week, format as dd/mm/yyyy às HH:mm
  const dateFormatter = new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
  return `${dateFormatter.format(messageDate)} às ${timePart}`;
}

/**
 * Formats a thread's last update time for display in the sidebar.
 * This format is optimized for scannability and compactness while providing key details.
 *
 * @param date - The date to format, as a string or Date object.
 * @returns A compact, localized date string (e.g., "Hoje às 10:30", "Ontem às 15:00", "23/07/25").
 */
export function formatSidebarDate(date: string | Date): string {
  const threadDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();

  const timeFormatter = new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
  );
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfToday.getDate() - 1);

  // If the thread was updated today, show "Hoje às HH:mm"
  if (threadDate >= startOfToday) {
    return `Hoje às ${timeFormatter.format(threadDate)}`;
  }

  // If the thread was updated yesterday, show "Ontem às HH:mm"
  if (threadDate >= startOfYesterday) {
    return `Ontem às ${timeFormatter.format(threadDate)}`;
  }

  // For older threads, show a compact date "dd/mm/yy"
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit',
  }).format(threadDate);
}

```
<!-- end of file -->

<!-- file -->
app/lib/utils.ts
```xml
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

```
<!-- end of file -->

<!-- file -->
app/root.tsx
```xml
import '@fontsource/geist-sans';
import '@fontsource/geist-mono';
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router';
import { useEffect } from 'react';
import { toast } from 'sonner';

import type { Route } from './+types/root';
import './app.css';
import { AuthProvider } from './auth/auth-provider';
import { Toaster } from '~/components/sonner';

export const links: Route.LinksFunction = () => [];

export function Layout({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const handleApiError = (
      event: CustomEvent<{
        message: string;
        description: string;
        action?: { label: string; onClick: () => void };
      }>,
    ) => {
      const { message, description, action } = event.detail;
      toast.error(message, { description, action });
    };

    window.addEventListener('apiError', handleApiError as EventListener);

    return () => {
      window.removeEventListener('apiError', handleApiError as EventListener);
    };
  }, []);

  return (
    <html lang="pt-BR">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <AuthProvider>
          {children}
          <Toaster />
          <ScrollRestoration />
          <Scripts />
        </AuthProvider>
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = 'Oops!';
  let details = 'An unexpected error occurred.';
  let stack: string | undefined;

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? '404' : 'Error';
    details =
      error.status === 404
        ? 'The requested page could not be found.'
        : error.statusText || details;
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message;
    stack = error.stack;
  }

  return (
    <main className="pt-16 p-4 container mx-auto">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full p-4 overflow-x-auto bg-muted text-muted-foreground rounded-md mt-4">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  );
}

```
<!-- end of file -->

<!-- file -->
app/routes/auth/callback.tsx
```xml
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from '~/auth/useAuth';
import { Skeleton } from '~/components/ui/skeleton';

export default function AuthCallback() {
  const { fetchUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuth = async () => {
      await fetchUser();
      navigate('/');
    };
    handleAuth();
  }, [fetchUser, navigate]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background">
      <div className="w-full max-w-md p-8 space-y-4 bg-card rounded-lg shadow">
        <h2 className="text-2xl font-bold text-center">Autenticando...</h2>
        <p className="text-center text-muted-foreground">
          Por favor, aguarde enquanto finalizamos seu login.
        </p>
        <div className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </div>
      </div>
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/routes/test.tsx
```xml
// apps/www/app/routes/test.tsx
import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '~/auth/useAuth';
import { useApi } from '~/hooks/useApi';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { Button } from '~/components/ui/button';

export default function TestPage() {
  const [testData, setTestData] = useState<Record<string, unknown> | null>(
    null,
  );
  const [userData, setUserData] = useState<Record<string, unknown> | null>(
    null,
  );
  const [typingText, setTypingText] = useState('');
  const [progress, setProgress] = useState(0);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [isProgressComplete, setIsProgressComplete] = useState(false);

  const { isAuthenticated, login } = useAuth();
  const { get, loading } = useApi();

  const fetchTestData = async () => {
    const data = await get<Record<string, unknown>>('/api/test');
    if (data) setTestData(data);
  };

  const fetchUserData = async () => {
    const userId = 'a1b2c3d4-e5f6-7890-1234-567890abcdef'; // Example UUID
    const data = await get<Record<string, unknown>>(`/api/user/${userId}`);
    if (data) setUserData(data);
  };

  const startSseStream = (
    url: string,
    onMessage: (data: Record<string, unknown>) => void,
    onComplete: () => void,
  ) => {
    if (!isAuthenticated) {
      toast.error('Login Required', {
        description: 'Please log in to start the stream.',
        action: {
          label: 'Login',
          onClick: () => login(),
        },
      });
      return;
    }

    const ctrl = new AbortController();

    fetchEventSource(url, {
      signal: ctrl.signal,
      credentials: 'include', // Ensures cookies are sent with the request

      async onopen(response) {
        if (response.ok) {
          return; // Connection is successful
        }

        if (response.status === 429) {
          throw new Error(
            'You have made too many requests. Please wait a moment before trying again.',
          );
        }

        throw new Error(
          `Failed to connect with status ${response.status}: ${response.statusText}`,
        );
      },

      onmessage(event) {
        // Only parse if the event data is not empty
        if (event.data) {
          const parsedData = JSON.parse(event.data);
          onMessage(parsedData);
          if (parsedData.isComplete) {
            onComplete();
            ctrl.abort(); // Close the connection on completion
          }
        }
      },

      onerror(err) {
        // This will now catch errors from onopen or other network issues
        toast.error('Stream Connection Error', {
          description:
            err.message || 'An unknown error occurred during the stream.',
        });
        ctrl.abort(); // Ensure connection is closed
        // Re-throwing the error is important to stop the library from retrying
        throw err;
      },
    });
  };

  const startTypingStream = () => {
    setTypingText('');
    setIsTypingComplete(false);
    startSseStream(
      '/api/stream/typing',
      (data) => setTypingText((prev) => prev + (data.char as string)),
      () => setIsTypingComplete(true),
    );
  };

  const startProgressStream = () => {
    setProgress(0);
    setIsProgressComplete(false);
    startSseStream(
      '/api/stream/progress',
      (data) => setProgress(data.progress as number),
      () => setIsProgressComplete(true),
    );
  };

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-foreground mb-8 text-center">
          Railway Connection Test
        </h1>

        <div className="grid gap-6">
          {/* HTTP Requests Section */}
          <div className="bg-card text-card-foreground rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">
              HTTP Requests (with Global Error Handling)
            </h2>
            <div className="space-y-4">
              <div>
                <Button onClick={fetchTestData} disabled={loading}>
                  {loading ? 'Loading...' : 'Fetch Test Data'}
                </Button>
                {testData && (
                  <div className="mt-3 p-3 bg-muted rounded-md">
                    <pre className="text-sm text-muted-foreground">
                      {JSON.stringify(testData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
              <div>
                <Button
                  onClick={fetchUserData}
                  disabled={loading}
                  variant="secondary"
                >
                  {loading ? 'Loading...' : 'Fetch User Data (Needs Auth)'}
                </Button>
                {userData && (
                  <div className="mt-3 p-3 bg-muted rounded-md">
                    <pre className="text-sm text-muted-foreground">
                      {JSON.stringify(userData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* SSE Streaming Section */}
          <div className="bg-card text-card-foreground rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">
              Server-Sent Events (SSE) Streaming (Needs Auth)
            </h2>
            <div className="space-y-6">
              {/* Typing Animation */}
              <div>
                <Button
                  onClick={startTypingStream}
                  className="bg-purple-500 hover:bg-purple-600"
                >
                  Start Typing Animation
                </Button>
                <div className="mt-3 p-4 bg-muted rounded-md min-h-[100px]">
                  <p className="text-muted-foreground leading-relaxed">
                    {typingText}
                    {!isTypingComplete && typingText && (
                      <span className="animate-pulse">|</span>
                    )}
                  </p>
                  {isTypingComplete && (
                    <p className="text-green-600 dark:text-green-400 text-sm mt-2">
                      ✓ Typing complete!
                    </p>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div>
                <Button
                  onClick={startProgressStream}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Start Progress Stream
                </Button>
                <div className="mt-3">
                  <div className="flex justify-between text-sm text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-200"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  {isProgressComplete && (
                    <p className="text-green-600 dark:text-green-400 text-sm mt-2">
                      ✓ Progress complete!
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

```
<!-- end of file -->

<!-- file -->
app/routes/thread.tsx
```xml
import type { Route } from './+types/thread';
import { ChatInterface } from '~/components/chat-interface';

export function meta({ params }: Route.MetaArgs) {
  return [
    {
      title: params.threadId
        ? `FUVEST AI - Thread ${params.threadId}`
        : 'FUVEST AI - Chat Inteligente',
    },
    {
      name: 'description',
      content: 'IA especializada em FUVEST para ajudar estudantes',
    },
  ];
}

export default function ThreadPage() {
  return <ChatInterface />;
}

```
<!-- end of file -->

<!-- file -->
app/routes.ts
```xml
import { type RouteConfig, index, route } from '@react-router/dev/routes';

export default [
  index('routes/thread.tsx'),
  route('c/:threadId', 'routes/thread.tsx', { id: 'chat-thread' }),
  route('test', 'routes/test.tsx'),
  route('auth/callback', 'routes/auth/callback.tsx'),
] satisfies RouteConfig;

```
<!-- end of file -->

<!-- file -->
app/services/chat-service.ts
```xml
// apps/www/app/services/chat-service.ts
import { apiClient } from '~/utils/axios-client';
import type { ChatMessage, Thread } from '~/types/chat';

/**
 * Fetches all threads for the authenticated user.
 */
export const fetchThreads = async (): Promise<Thread[]> => {
  const response = await apiClient.get<{ data: Thread[] }>('/api/chat/threads');
  return response.data;
};

/**
 * Fetches the message history for a specific thread.
 * @param threadId - The ID of the thread to fetch.
 */
export const fetchMessages = async (
  threadId: string,
): Promise<ChatMessage[]> => {
  const response = await apiClient.get<{ data: ChatMessage[] }>(
    `/api/chat/history/${threadId}`,
  );
  return response.data;
};

/**
 * Creates a new chat thread.
 * @param title - The initial title for the thread.
 */
export const createThread = async (title: string): Promise<Thread> => {
  const response = await apiClient.post<{ data: Thread }>('/api/chat/threads', {
    title,
  });
  return response.data;
};

/**
 * Sends a new message in a thread.
 * @param content - The content of the message.
 * @param threadId - The ID of the thread.
 * @param parentId - The ID of the parent message, if any.
 * @param idempotencyKey - A unique key to prevent duplicate messages.
 */
export const sendMessage = async (
  content: string,
  threadId: string,
  parentId: string | null,
  idempotencyKey: string,
): Promise<ChatMessage> => {
  const response = await apiClient.post<{ data: ChatMessage }>(
    '/api/chat/message',
    {
      content,
      threadId,
      parentId,
      idempotencyKey,
      model: 'gemini-2.5-flash-lite',
    },
  );
  return response.data;
};

/**
 * Edits an existing message, creating a new sibling branch.
 * @param messageId - The ID of the message to edit.
 * @param content - The new content for the message.
 * @returns The updated conversation history.
 */
export const editMessage = async (
  messageId: string,
  content: string,
): Promise<ChatMessage[]> => {
  const response = await apiClient.put<{ data: ChatMessage[] }>(
    `/api/chat/message/${messageId}`,
    { content },
  );
  return response.data;
};

/**
 * Deletes a message and its descendants.
 * @param messageId - The ID of the message to delete.
 */
export const deleteMessage = async (
  messageId: string,
): Promise<ChatMessage[]> => {
  const response = await apiClient.delete<{ data: ChatMessage[] }>(
    `/api/chat/message/${messageId}`,
  );
  return response.data;
};

/**
 * Navigates to a sibling message (a different AI response to the same prompt).
 * @param messageId - The ID of the current message.
 * @param direction - The direction to navigate ('next' or 'prev').
 */
export const navigateSibling = async (
  messageId: string,
  direction: 'next' | 'prev',
): Promise<ChatMessage[]> => {
  const response = await apiClient.post<{ data: ChatMessage[] }>(
    '/api/chat/navigate',
    { messageId, direction },
  );
  return response.data;
};

```
<!-- end of file -->

<!-- file -->
app/store/chatStore.ts
```xml
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { ChatMessage, Thread } from '~/types/chat';

export interface ChatState {
  threads: Thread[];
  currentThreadId: string | null;
  messagesByThread: Record<string, ChatMessage[]>;
  streamingResponses: Record<string, ChatMessage>;
  threadsStatus: 'idle' | 'loading' | 'success' | 'error';
  inputValue: string;
  unreadThreads: string[];
}

interface ChatActions {
  setThreads: (threads: Thread[]) => void;
  addThread: (thread: Thread) => void;
  updateThread: (threadId: string, title: string) => void;
  setThreadsStatus: (status: ChatState['threadsStatus']) => void;
  setCurrentThreadId: (threadId: string | null) => void;
  setMessagesForThread: (threadId: string, messages: ChatMessage[]) => void;
  addMessageToThread: (threadId: string, message: ChatMessage) => void;
  replaceMessageInThread: (
    threadId: string,
    messageIdToReplace: string,
    newMessage: ChatMessage,
  ) => void;
  removeMessageFromThread: (threadId: string, messageId: string) => void;
  startStreaming: (threadId: string, message: ChatMessage) => void;
  updateStreamingContent: (
    threadId: string,
    contentChunk: string,
    debugInfo?: ChatMessage['debug'],
  ) => void;
  finalizeStreaming: (threadId: string, finalMessage: ChatMessage) => void;
  abortStreaming: (threadId: string) => void;
  getThreadById: (id: string | null) => Thread | undefined;
  setInputValue: (value: string) => void;
  addUnreadThread: (threadId: string) => void;
  removeUnreadThread: (threadId: string) => void;
}

type ChatStore = ChatState & ChatActions;

const THIRTY_MINUTES_IN_MS = 30 * 60 * 1000;

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // --- INITIAL STATE ---
      threads: [],
      currentThreadId: null,
      messagesByThread: {},
      streamingResponses: {},
      threadsStatus: 'idle',
      inputValue: '',
      unreadThreads: [],

      // --- ACTIONS ---
      setThreads: (threads) => set({ threads }),
      addThread: (thread) =>
        set((state) => ({ threads: [thread, ...state.threads] })),
      updateThread: (threadId, title) =>
        set((state) => ({
          threads: state.threads.map((t) =>
            t.id === threadId ? { ...t, title } : t,
          ),
        })),
      setThreadsStatus: (status) => set({ threadsStatus: status }),
      setCurrentThreadId: (threadId) => set({ currentThreadId: threadId }),
      setMessagesForThread: (threadId, messages) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: messages,
          },
        })),
      addMessageToThread: (threadId, message) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: [...(state.messagesByThread[threadId] || []), message],
          },
        })),
      replaceMessageInThread: (threadId, messageIdToReplace, newMessage) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).map((m) =>
              m.id === messageIdToReplace ? newMessage : m,
            ),
          },
        })),
      removeMessageFromThread: (threadId, messageId) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).filter(
              (m) => m.id !== messageId,
            ),
          },
        })),
      startStreaming: (threadId, message) => {
        set((state) => ({
          streamingResponses: {
            ...state.streamingResponses,
            [threadId]: message,
          },
        }));
      },
      updateStreamingContent: (threadId, contentChunk, debugInfo) => {
        set((state) => {
          const streamingMessage = state.streamingResponses[threadId];
          if (!streamingMessage) return {};
          return {
            streamingResponses: {
              ...state.streamingResponses,
              [threadId]: {
                ...streamingMessage,
                content: streamingMessage.content + contentChunk,
                ...(debugInfo && { debug: debugInfo }),
              },
            },
          };
        });
      },
      finalizeStreaming: (threadId, finalMessage) => {
        get().addMessageToThread(threadId, finalMessage);
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      abortStreaming: (threadId) => {
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      getThreadById: (id) => get().threads.find((t) => t.id === id),
      setInputValue: (value) => set({ inputValue: value }),
      addUnreadThread: (threadId) =>
        set((state) => ({
          // Use a Set to prevent duplicates
          unreadThreads: [...new Set([...state.unreadThreads, threadId])],
        })),
      removeUnreadThread: (threadId) =>
        set((state) => ({
          unreadThreads: state.unreadThreads.filter((id) => id !== threadId),
        })),
    }),
    {
      name: 'chat-storage', // storage name
      storage: createJSONStorage(() => sessionStorage), // use sessionStorage
      // On save, create an object with the value and a timestamp
      partialize: (state) => ({
        inputValue: {
          value: state.inputValue,
          timestamp: Date.now(),
        },
      }),
      // On load, check the timestamp before merging the state
      merge: (persistedState, currentState) => {
        const typedState = persistedState as {
          inputValue?: { value: string; timestamp: number };
        };

        if (typedState.inputValue) {
          const { timestamp } = typedState.inputValue;
          if (Date.now() - timestamp > THIRTY_MINUTES_IN_MS) {
            // Data is stale, ignore it and return the initial state.
            return currentState;
          }
        }

        // Data is fresh, so we manually merge the unwrapped value.
        return {
          ...currentState,
          ...(persistedState as object),
          inputValue: typedState.inputValue?.value || currentState.inputValue,
        };
      },
    },
  ),
);

```
<!-- end of file -->

<!-- file -->
app/types/chat.d.ts
```xml
// apps/www/app/types/chat.ts

/**
 * Represents a single message in a conversation, including UI-specific state.
 */
export interface ChatMessage {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  createdAt: string;
  model?: string;
  threadId: string;
  parentId: string | null;
  siblingCount: number;
  siblingPosition: number;
  deletedAt?: string | null;
  debug?: {
    serverTimestamp?: number;
    tps?: number;
    ping?: number;
    avgPing?: number;
    maxPing?: number;
    avgTps?: number;
    maxTps?: number;
  };
  /** Optional title for the thread, returned with the first message of a new thread. */
  threadTitle?: string;
}

/**
 * Represents a conversation thread.
 */
export interface Thread {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  // This might not be needed on the summary object, but is fine for now
  messages: ChatMessage[];
}

```
<!-- end of file -->

<!-- file -->
app/utils/axios-client.ts
```xml
import axios, { type AxiosInstance, type AxiosError } from 'axios';
import { useChatStore } from '~/store/chatStore';

interface ApiError {
  message: string;
  statusCode: number;
}

interface ApiErrorEventDetail {
  message: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

class AxiosApiClient {
  private instance: AxiosInstance;
  private refreshPromise: Promise<string | null> | null = null;

  constructor() {
    this.instance = axios.create({
      baseURL: '/',
      withCredentials: true,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  /**
   * Dispatches a global custom event for API errors to be caught by the UI.
   */
  private dispatchErrorEvent(detail: ApiErrorEventDetail) {
    const event = new CustomEvent<ApiErrorEventDetail>('apiError', {
      detail,
    });
    window.dispatchEvent(event);
  }

  /**
   * Translates an AxiosError into a user-friendly notification.
   */
  private handleApiError(error: AxiosError<ApiError>) {
    const status = error.response?.data?.statusCode || error.response?.status;
    const message =
      error.response?.data?.message || 'An unknown error occurred.';

    switch (status) {
      case 401:
        // Get the current input value from the store and save it if it exists.
        const unsentMessage = useChatStore.getState().inputValue;
        if (unsentMessage && unsentMessage.trim()) {
          localStorage.setItem('unsentChatMessage', unsentMessage);
        }

        this.dispatchErrorEvent({
          message: 'Authentication required',
          description: 'Your session may have expired. Please log in again.',
          action: {
            label: 'Login',
            onClick: () => (window.location.href = '/api/auth/google'),
          },
        });
        break;
      case 429:
        this.dispatchErrorEvent({
          message: 'Rate limit exceeded',
          description:
            'You have made too many requests. Please try again later.',
        });
        break;
      default:
        this.dispatchErrorEvent({
          message: 'An error occurred',
          description: message,
        });
        break;
    }
  }

  /**
   * Sets up response interceptors to handle token refreshes and global errors.
   */
  private setupInterceptors() {
    this.instance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError<ApiError>) => {
        const originalRequest = error.config as {
          _retry?: boolean;
          url?: string;
        };

        const is401Error = error.response?.status === 401;
        const isRefreshEndpoint = originalRequest.url === '/api/auth/refresh';

        // If the 401 was from the refresh endpoint itself, or if this is already a retry, give up.
        if (is401Error && (isRefreshEndpoint || originalRequest._retry)) {
          this.handleApiError(error);
          return Promise.reject(error);
        }

        // If it's any other 401, attempt to refresh the token.
        if (is401Error) {
          originalRequest._retry = true;
          try {
            await this.refreshAccessToken();
            return this.instance(originalRequest);
          } catch (refreshError) {
            // The refreshAccessToken promise rejected, so we can't recover.
            // The handleApiError was already called in the initial 401 check.
            return Promise.reject(refreshError);
          }
        }

        // Handle other errors that are not 401s (e.g., 500, 404).
        this.handleApiError(error);
        return Promise.reject(error);
      },
    );
  }

  /**
   * Manages the token refresh process, ensuring it only runs once at a time.
   */
  private async refreshAccessToken(): Promise<string | null> {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.instance
      .post('/api/auth/refresh')
      .then((response) => response.data.accessToken)
      .catch((error) => {
        // If the refresh itself fails, we reject the promise so the interceptor can handle it.
        return Promise.reject(error);
      })
      .finally(() => {
        this.refreshPromise = null;
      });

    return this.refreshPromise;
  }

  // --- Public API Methods ---

  public async get<T>(url: string, config = {}): Promise<T> {
    const response = await this.instance.get<T>(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.post<T>(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.put<T>(url, data, config);
    return response.data;
  }

  public async patch<T>(url: string, data?: unknown, config = {}): Promise<T> {
    const response = await this.instance.patch<T>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config = {}): Promise<T> {
    const response = await this.instance.delete<T>(url, config);
    return response.data;
  }
}

export const apiClient = new AxiosApiClient();

```
<!-- end of file -->

<!-- file -->
components.json
```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "",
    "css": "app/app.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "~/components",
    "utils": "~/lib/utils",
    "ui": "~/components/ui",
    "lib": "~/lib",
    "hooks": "~/hooks"
  },
  "iconLibrary": "lucide"
}

```
<!-- end of file -->

<!-- file -->
eslint.config.js
```javascript
import { config } from '@revisa/eslint-config/react.js';

export default config;

```
<!-- end of file -->

<!-- file -->
package.json
```json
{
  "name": "www",
  "private": true,
  "type": "module",
  "scripts": {
    "build": "react-router build",
    "dev": "dotenv -e .env -- react-router dev",
    "typecheck": "react-router typegen && tsc",
    "lint": "eslint ."
  },
  "dependencies": {
    "@microsoft/fetch-event-source": "^2.0.1",
    "@radix-ui/react-avatar": "^1.1.10",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-slot": "^1.2.3",
    "@react-router/node": "^7.5.3",
    "@react-router/serve": "^7.5.3",
    "@tanstack/react-virtual": "^3.13.12",
    "axios": "^1.10.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "dompurify": "^3.2.6",
    "eventsource-parser": "^3.0.3",
    "isbot": "^5.1.27",
    "katex": "^0.16.22",
    "lucide-react": "^0.525.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-markdown": "^10.1.0",
    "react-router": "^7.5.3",
    "rehype-katex": "^7.0.1",
    "remark-math": "^6.0.0",
    "sonner": "^2.0.6",
    "tailwind-merge": "^3.3.1",
    "zustand": "^5.0.6"
  },
  "devDependencies": {
    "@react-router/dev": "^7.5.3",
    "@revisa/eslint-config": "*",
    "@revisa/typescript-config": "*",
    "@tailwindcss/vite": "^4.1.4",
    "@types/dompurify": "^3.0.5",
    "@types/katex": "^0.16.7",
    "@types/node": "^20",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "dotenv-cli": "^8.0.0",
    "prettier": "^3.4.2",
    "rollup-plugin-visualizer": "^6.0.3",
    "tailwindcss": "^4.1.4",
    "tw-animate-css": "^1.3.5",
    "typescript": "^5.8.3",
    "vite": "^6.3.3",
    "vite-tsconfig-paths": "^5.1.4"
  }
}

```
<!-- end of file -->

<!-- file -->
railway.json
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "apps/www/Dockerfile",
    "watchPatterns": [
      "apps/www/**",
      "packages/**",
      "package.json",
      "package-lock.json",
      "turbo.json"
    ]
  },
  "deploy": {
    "numReplicas": 1,
    "sleepApplication": false,
    "restartPolicyType": "ON_FAILURE",
    "healthcheckPath": "/",
    "healthcheckTimeout": 100
  }
}

```
<!-- end of file -->

<!-- file -->
react-router.config.ts
```xml
import type { Config } from '@react-router/dev/config';

export default {
  // Config options...
  // Server-side render by default, to enable SPA mode set this to `false`
  ssr: false,
} satisfies Config;

```
<!-- end of file -->

<!-- file -->
tsconfig.json
```json
{
  "extends": "@revisa/typescript-config/react.json",
  "include": [
    "**/*",
    "**/.server/**/*",
    "**/.client/**/*",
    ".react-router/types/**/*"
  ],
  "compilerOptions": {
    "rootDirs": [".", "./.react-router/types"],
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"]
    },
    "verbatimModuleSyntax": true,
    "types": ["node", "vite/client"]
  }
}

```
<!-- end of file -->

<!-- file -->
vite.config.ts
```xml
import { reactRouter } from '@react-router/dev/vite';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';
import { visualizer } from 'rollup-plugin-visualizer';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig(({ command, isSsrBuild }) => ({
  plugins: [
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
    command === 'build' &&
      !isSsrBuild &&
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: 'stats.html',
      }),
  ],
  server: {
    host: true,
    port: Number(process.env.WWW_PORT) || 3000,
    proxy: {
      // Proxy API requests to the NestJS server
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
      },
    },
  },
}));

```
<!-- end of file -->


# Project Structure:

|-- .dockerignore
|-- .env
|-- .env.example
|-- Caddyfile
|-- Dockerfile
|-- Dockerfile.dev
|-- app
    |-- app.css
    |-- auth
        |-- auth-provider.tsx
        |-- useAuth.ts
    |-- components
        |-- auto-textarea.tsx
        |-- chat
            |-- chat-header.tsx
            |-- chat-input.tsx
            |-- chat-sidebar.tsx
            |-- markdown-renderer.tsx
            |-- message-list.tsx
        |-- chat-interface.tsx
        |-- chat-skeleton.tsx
        |-- sonner.tsx
        |-- ui
            |-- avatar.tsx
            |-- button.tsx
            |-- dropdown-menu.tsx
            |-- input.tsx
            |-- skeleton.tsx
    |-- hooks
        |-- useApi.ts
        |-- useChatActions.ts
        |-- useChatConnection.ts
        |-- useChatForm.ts
        |-- useMediaQuery.ts
    |-- lib
        |-- date-utils.ts
        |-- utils.ts
    |-- root.tsx
    |-- routes
        |-- auth
            |-- callback.tsx
        |-- test.tsx
        |-- thread.tsx
    |-- routes.ts
    |-- services
        |-- chat-service.ts
    |-- store
        |-- chatStore.ts
    |-- types
        |-- chat.d.ts
    |-- utils
        |-- axios-client.ts
|-- components.json
|-- eslint.config.js
|-- package.json
|-- railway.json
|-- react-router.config.ts
|-- tsconfig.json
|-- vite.config.ts

<!-- end of codebase -->
