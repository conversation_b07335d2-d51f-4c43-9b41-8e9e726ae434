/**
 * Formats a date for display in the message list, localized for Brazilian Portuguese.
 * Provides user-friendly relative times like "Hoje às 10:30" or "Ontem às 15:00".
 *
 * @param date - The date to format, as a string or Date object.
 * @returns A formatted, localized date string.
 */
export function formatMessageDate(date: string | Date): string {
  const messageDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();

  const timeFormatter = new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
  );
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfToday.getDate() - 1);

  // Use a rolling 7-day window from the start of today
  const sevenDaysAgo = new Date(startOfToday);
  sevenDaysAgo.setDate(startOfToday.getDate() - 6);

  const timePart = timeFormatter.format(messageDate);

  // Check if the message was sent in the last 60 seconds
  const diffInSeconds = (now.getTime() - messageDate.getTime()) / 1000;
  if (diffInSeconds < 60) {
    return 'agora mesmo';
  }

  // Check if the date is today
  if (messageDate >= startOfToday) {
    return `Hoje às ${timePart}`;
  }

  // Check if the date was yesterday
  if (messageDate >= startOfYesterday) {
    return `Ontem às ${timePart}`;
  }

  // Check if the date was in the last 7 days
  if (messageDate >= sevenDaysAgo) {
    const weekdayFormatter = new Intl.DateTimeFormat('pt-BR', {
      weekday: 'long',
    });
    const weekday = weekdayFormatter.format(messageDate);
    // Capitalize the first letter
    const capitalizedWeekday =
      weekday.charAt(0).toUpperCase() + weekday.slice(1);
    return `${capitalizedWeekday} às ${timePart}`;
  }

  // For dates older than a week, format as dd/mm/yyyy às HH:mm
  const dateFormatter = new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
  return `${dateFormatter.format(messageDate)} às ${timePart}`;
}

/**
 * Formats a thread's last update time for display in the sidebar.
 * This format is optimized for scannability and compactness while providing key details.
 *
 * @param date - The date to format, as a string or Date object.
 * @returns A compact, localized date string (e.g., "Hoje às 10:30", "Ontem às 15:00", "23/07/25").
 */
export function formatSidebarDate(date: string | Date): string {
  const threadDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();

  const timeFormatter = new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const startOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
  );
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfToday.getDate() - 1);

  // If the thread was updated today, show "Hoje às HH:mm"
  if (threadDate >= startOfToday) {
    return `Hoje às ${timeFormatter.format(threadDate)}`;
  }

  // If the thread was updated yesterday, show "Ontem às HH:mm"
  if (threadDate >= startOfYesterday) {
    return `Ontem às ${timeFormatter.format(threadDate)}`;
  }

  // For older threads, show a compact date "dd/mm/yy"
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit',
  }).format(threadDate);
}
