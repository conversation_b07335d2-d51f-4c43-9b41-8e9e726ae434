import * as React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Edit3,
  Trash2,
  <PERSON>,
  <PERSON>,
  <PERSON>otateCcw,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Button } from '~/components/ui/button';
import { AutoTextarea } from '../auto-textarea';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';
import { formatMessageDate } from '~/lib/date-utils';
import type { ChatMessage } from '~/types/chat.ts';
import { useChatActions } from '~/hooks/useChatActions';
import MarkdownRenderer from './markdown-renderer';

// Define the actions that MessageComponent will need
interface MessageActions {
  saveEdit: (messageId: string, newContent: string) => void;
  copyMessage: (content: string) => void;
  deleteMessage: (messageId: string) => void;
  regenerateResponse: (aiMessageId: string) => void;
  navigateSibling: (messageId: string, direction: 'next' | 'prev') => void;
}

interface MessageComponentProps {
  message: ChatMessage;
  actions: MessageActions; // Actions are now passed as a prop
  isLastMessage?: boolean;
  isStreaming?: boolean;
}

const MessageComponent = React.memo(function MessageComponent({
  message,
  actions, // Destructure actions from props
  isLastMessage = false,
  isStreaming = false,
}: MessageComponentProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState(message.content);

  const handleSave = () => {
    // Only save if content has actually changed
    if (editValue.trim() !== message.content.trim()) {
      actions.saveEdit(message.id, editValue);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(message.content);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
    }
  };

  const isDeleted = !!message.deletedAt;
  const isAI = message.role === 'ASSISTANT';

  return (
    <div
      id={message.id}
      className={cn(
        'w-full border-b border-border group transition-colors',
        isDeleted
          ? 'bg-destructive/10 hover:bg-destructive/12'
          : isAI
            ? 'bg-muted hover:bg-border'
            : 'bg-background hover:bg-muted',
      )}
    >
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3 items-start">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback
              className={cn(
                'text-xs font-medium',
                isAI
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground',
              )}
            >
              {isAI ? 'AI' : 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {isAI ? 'FUVEST AI' : 'Você'}
                </span>
                {isAI && message.model && (
                  <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                    {message.model}
                  </span>
                )}
              </div>
              {message.siblingCount > 1 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition <= 1}
                    onClick={() => actions.navigateSibling(message.id, 'prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span>
                    {message.siblingPosition}/{message.siblingCount}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition >= message.siblingCount}
                    onClick={() => actions.navigateSibling(message.id, 'next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex-grow">
                {isEditing ? (
                  <div className="space-y-2">
                    <AutoTextarea
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      onKeyDown={handleEditKeyDown}
                      className="flex-1 min-h-[60px] text-sm"
                    />
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" onClick={handleSave}>
                        <Check className="h-3 w-3 mr-1" />
                        Salvar
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Cancelar
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div
                    className={cn(
                      'relative text-sm leading-relaxed text-foreground pr-11 break-words [hyphens:auto]',
                      isDeleted && 'text-muted-foreground italic',
                    )}
                  >
                    {isDeleted ? (
                      'This message was deleted on this branch.'
                    ) : (
                      <MarkdownRenderer content={message.content} />
                    )}
                    {/* Blinking cursor for streaming messages */}
                    {isStreaming && !isDeleted && (
                      <span className="animate-pulse">|</span>
                    )}
                  </div>
                )}
              </div>

              <div
                className={cn(
                  'sticky message-footer bottom-0 z-10 flex items-center justify-between pt-2 transition-colors',
                  isDeleted
                    ? 'bg-transparent'
                    : isAI
                      ? 'bg-muted group-hover:bg-border'
                      : 'bg-background group-hover:bg-muted',
                )}
              >
                <span className="text-xs text-muted-foreground">
                  {formatMessageDate(message.createdAt)}
                </span>
                {!isDeleted && (
                  <div
                    className={cn(
                      'flex items-center gap-1 transition-opacity duration-200',
                      isLastMessage
                        ? 'opacity-100'
                        : 'opacity-0 group-hover:opacity-100',
                    )}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => actions.copyMessage(message.content)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    {isAI && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => actions.regenerateResponse(message.id)}
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Settings className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleEdit}>
                          <Edit3 className="h-3 w-3 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => actions.deleteMessage(message.id)}
                          variant="destructive"
                        >
                          <Trash2 className="h-3 w-3 mr-2" />
                          Excluir branch
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

const SkeletonMessage = React.memo(function SkeletonMessage() {
  return (
    <div className="w-full border-b border-border bg-muted">
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback className="bg-primary/20 text-primary-foreground text-xs font-medium">
              AI
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-muted-foreground">
                FUVEST AI
              </span>
            </div>
            <div className="space-y-2 pr-11">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

interface MessageListProps {
  messages: ChatMessage[];
  streamingResponse: ChatMessage | null;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
}

export function MessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  // Call useChatActions() ONCE here in the parent component
  const chatActions = useChatActions();

  return (
    <div className="flex-1 overflow-y-scroll">
      <div className="w-full">
        {messages.map((message, index) => (
          <MessageComponent
            key={`${message.id}-${message.siblingPosition}`}
            message={message}
            isLastMessage={index === messages.length - 1 && !streamingResponse}
            isStreaming={false}
            actions={chatActions} // Pass the actions object down as a prop
          />
        ))}
        {streamingResponse && (
          <>
            {streamingResponse.content.length === 0 ? (
              <SkeletonMessage />
            ) : (
              <MessageComponent
                message={streamingResponse}
                isLastMessage={false}
                isStreaming={true}
                actions={chatActions} // Also pass to the streaming message component
              />
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}

// Virtualized version of MessageList for better performance with large message lists
// Smart message list that automatically chooses between regular and virtualized rendering
export function SmartMessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  // Use virtualization only for larger message lists (50+ messages)
  // This avoids unnecessary overhead for small conversations
  const shouldVirtualize = messages.length >= 50;

  if (shouldVirtualize) {
    return (
      <VirtualizedMessageList
        messages={messages}
        streamingResponse={streamingResponse}
        messagesEndRef={messagesEndRef}
      />
    );
  }

  return (
    <MessageList
      messages={messages}
      streamingResponse={streamingResponse}
      messagesEndRef={messagesEndRef}
    />
  );
}

export function VirtualizedMessageList({
  messages,
  streamingResponse,
  messagesEndRef,
}: MessageListProps) {
  const chatActions = useChatActions();
  const parentRef = React.useRef<HTMLDivElement>(null);

  // Combine messages with streaming response for virtualization
  const allItems = React.useMemo(() => {
    const items = [...messages];
    if (streamingResponse) {
      items.push(streamingResponse);
    }
    return items;
  }, [messages, streamingResponse]);

  // Dynamic height estimation based on message content
  const estimateSize = React.useCallback(
    (index: number) => {
      const message = allItems[index];
      if (!message) return 120;

      // Base height for message structure (avatar, padding, etc.)
      let estimatedHeight = 80;

      // Estimate additional height based on content length
      const contentLength = message.content.length;
      const estimatedLines = Math.max(1, Math.ceil(contentLength / 80)); // ~80 chars per line
      estimatedHeight += estimatedLines * 24; // ~24px per line

      // Add extra height for code blocks and math expressions
      const codeBlockMatches = message.content.match(/```[\s\S]*?```/g) || [];
      const mathBlockMatches = message.content.match(/\$\$[\s\S]*?\$\$/g) || [];
      estimatedHeight +=
        codeBlockMatches.length * 100 + mathBlockMatches.length * 60;

      // Add height for lists and other markdown elements
      const listMatches = message.content.match(/^[\s]*[-*+]\s/gm) || [];
      estimatedHeight += listMatches.length * 28;

      return Math.max(120, estimatedHeight);
    },
    [allItems],
  );

  // Create virtualizer with dynamic sizing
  const virtualizer = useVirtualizer({
    count: allItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 8, // Render 8 extra items above/below visible area for smooth scrolling
    measureElement: (element) => {
      // Use the actual measured height when available
      return element?.getBoundingClientRect().height ?? estimateSize(0);
    },
    // Enable smooth scrolling optimizations
    scrollMargin: parentRef.current?.offsetTop ?? 0,
    gap: 0, // No gap between items since we handle spacing in CSS
  });

  // Track if user has manually scrolled up
  const [shouldAutoScroll, setShouldAutoScroll] = React.useState(true);
  const isInitialLoad = React.useRef(true);

  // Handle scroll events to detect user scrolling
  React.useEffect(() => {
    const scrollElement = parentRef.current;
    if (!scrollElement) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold

      setShouldAutoScroll(isAtBottom);
    };

    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-scroll to bottom when new messages arrive (only if user hasn't scrolled up)
  React.useEffect(() => {
    if (allItems.length > 0 && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      const behavior = isInitialLoad.current ? 'auto' : 'smooth';

      virtualizer.scrollToIndex(lastIndex, {
        align: 'end',
        behavior,
      });

      if (isInitialLoad.current) {
        isInitialLoad.current = false;
      }
    }
  }, [allItems.length, virtualizer, shouldAutoScroll]);

  // Auto-scroll during streaming (content updates)
  React.useEffect(() => {
    if (streamingResponse && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      virtualizer.scrollToIndex(lastIndex, {
        align: 'end',
        behavior: 'auto', // Use auto for streaming to avoid jank
      });
    }
  }, [
    streamingResponse?.content,
    virtualizer,
    shouldAutoScroll,
    allItems.length,
  ]);

  return (
    <div ref={parentRef} className="flex-1 overflow-y-scroll">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const message = allItems[virtualItem.index];
          const isLastMessage =
            virtualItem.index === allItems.length - 1 && !streamingResponse;
          const isStreamingMessage = !!(
            streamingResponse && message.id === streamingResponse.id
          );

          return (
            <div
              key={`${message.id}-${message.siblingPosition}`}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
              ref={virtualizer.measureElement}
              data-index={virtualItem.index}
            >
              {isStreamingMessage && streamingResponse?.content.length === 0 ? (
                <SkeletonMessage />
              ) : (
                <MessageComponent
                  message={message}
                  isLastMessage={isLastMessage}
                  isStreaming={isStreamingMessage}
                  actions={chatActions}
                />
              )}
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
