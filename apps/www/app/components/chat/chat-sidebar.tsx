import {
  MessageCircle,
  HelpCircle,
  BookOpen,
  TrendingUp,
  Plus,
} from 'lucide-react';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '~/components/ui/dropdown-menu';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';
import { formatSidebarDate } from '~/lib/date-utils';
import { useAuth } from '~/auth/useAuth';
import type { Thread } from '~/types/chat';
import { Link } from 'react-router';
import { useChatActions } from '~/hooks/useChatActions';

interface ChatSidebarProps {
  threads: Thread[];
  currentThreadId: string | null;
  onNewThread: () => void;
  unsentMessage: string;
  unreadThreads: string[];
}

const navigationItems = [
  {
    title: 'Chat IA',
    icon: MessageCircle,
    url: '#',
    isActive: true,
  },
  {
    title: 'Questões',
    icon: HelpCircle,
    url: '#',
  },
  {
    title: 'Simulados',
    icon: BookOpen,
    url: '#',
  },
  {
    title: 'Progresso',
    icon: TrendingUp,
    url: '#',
  },
];

export function ChatSidebar({
  threads,
  currentThreadId,
  onNewThread,
  unsentMessage,
  unreadThreads,
}: ChatSidebarProps) {
  const { isAuthenticated, user, login, logout, isLoading } = useAuth();
  const { prefetchThread } = useChatActions();

  return (
    <div className="w-64 border-r border-border bg-background flex-col hidden lg:flex">
      {/* Sidebar Header */}
      <div className="h-14 border-b border-border px-4 flex items-center">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded bg-primary text-primary-foreground font-bold text-sm">
            F
          </div>
          <span className="font-semibold text-sm">FUVEST AI</span>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 px-2 py-2">
        <div className="space-y-1">
          {navigationItems.map((item) => (
            <a
              key={item.title}
              href={item.url}
              className={cn(
                'flex items-center gap-3 h-9 w-full px-3 rounded-md text-sm transition-colors',
                item.isActive
                  ? 'bg-accent text-accent-foreground'
                  : 'hover:bg-accent text-foreground',
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </a>
          ))}
        </div>

        {/* Thread List */}
        <div className="mt-4">
          <div className="flex items-center justify-between px-3 mb-2">
            <h3 className="text-sm font-semibold text-foreground">Conversas</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={onNewThread}
              className="h-6 w-6 p-0"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="space-y-1 overflow-y-scroll max-h-98">
            {(threads || []).map((thread) => (
              <Link
                key={thread.id}
                to={`/c/${thread.id}`}
                onMouseEnter={() => prefetchThread(thread.id)}
                className={cn(
                  'w-full text-left px-3 py-2 rounded-md text-sm transition-colors flex items-center gap-2',
                  currentThreadId === thread.id
                    ? 'bg-accent text-accent-foreground'
                    : 'hover:bg-accent text-foreground',
                )}
              >
                <div className="flex-1 truncate">
                  <div className="truncate">
                    {thread.title || 'Nova conversa'}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatSidebarDate(thread.updatedAt)}
                  </div>
                </div>
                {unreadThreads.includes(thread.id) && (
                  <span className="h-2 w-2 shrink-0 block rounded-full bg-primary" />
                )}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Sidebar Footer */}
      <div className="h-16 border-t border-border px-4 flex items-center">
        {isLoading ? (
          <Skeleton className="h-8 w-full" />
        ) : isAuthenticated && user ? (
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center gap-2 w-full text-left p-1 rounded-md hover:bg-accent">
              <Avatar className="h-8 w-8">
                {user.picture && <img src={user.picture} alt={user.name} />}
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1 truncate">
                <p className="text-sm font-semibold truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuItem>Minha Conta</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} variant="destructive">
                Sair
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button onClick={() => login(unsentMessage)} className="w-full">
            Fazer Login com Google
          </Button>
        )}
      </div>
    </div>
  );
}
