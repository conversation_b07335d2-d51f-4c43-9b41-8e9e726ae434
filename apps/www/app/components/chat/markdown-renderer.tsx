import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';

interface MarkdownRendererProps {
  content: string;
}

/**
 * A component that renders markdown content, including LaTeX math expressions.
 * It leverages react-markdown with remark-math and rehype-katex.
 * This component is streaming-aware; incomplete markdown or LaTeX will be
 * rendered as plain text until it becomes valid.
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath]}
      rehypePlugins={[rehypeKatex]}
      components={{
        p: ({ node, ...props }) => <p className="mb-2 last:mb-0" {...props} />,
        strong: ({ node, ...props }) => (
          <strong className="font-semibold" {...props} />
        ),
        a: ({ node, ...props }) => (
          <a
            className="text-primary hover:underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul className="list-disc list-outside my-2 pl-6" {...props} />
        ),
        ol: ({ node, ...props }) => (
          <ol className="list-decimal list-outside my-2 pl-6" {...props} />
        ),
        li: ({ node, ...props }) => <li className="mb-1" {...props} />,
        code: ({ node, className, children, ...props }) => {
          // Check if the className contains a language definition (e.g., "language-js").
          // This is a reliable way to distinguish fenced code blocks from inline code.
          const match = /language-(\w+)/.exec(className || '');

          return !match ? (
            // Render INLINE code
            <code
              className="bg-muted text-muted-foreground rounded-sm px-1.5 py-1 font-mono text-sm"
              {...props}
            >
              {children}
            </code>
          ) : (
            // Render BLOCK code
            <div className="bg-gray-900 text-white my-2 rounded-md">
              <div className="text-xs text-muted-foreground px-3 pt-2 capitalize flex justify-between items-center">
                <span>{match[1]}</span>
                <button
                  onClick={() =>
                    navigator.clipboard.writeText(
                      String(children).replace(/\n$/, ''),
                    )
                  }
                  className="text-xs hover:text-white"
                >
                  Copy
                </button>
              </div>
              <pre className="p-3 overflow-x-auto">
                <code className="text-sm font-mono" {...props}>
                  {children}
                </code>
              </pre>
            </div>
          );
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default React.memo(MarkdownRenderer);
