import { useRef, useCallback, useEffect } from 'react';
import type { ChangeEvent, Ref } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useChatStore } from '~/store/chatStore';
import type { AutoTextareaRef } from '~/components/auto-textarea';

interface UseChatFormProps {
  onSubmit: (value: string) => void;
  isAiTyping: boolean;
  currentThreadId?: string | null;
}

/**
 * Manages the state and logic of the chat input form, including
 * text input, enter key handling, and focusing.
 *
 * @param onSubmit - Callback function to execute when the form is submitted.
 * @param isAiTyping - <PERSON>olean to disable the form during AI responses.
 * @returns An object with form state and handlers.
 */
export function useChatForm({ onSubmit, isAiTyping, currentThreadId }: UseChatFormProps) {
  const { inputValue, setInputValue } = useChatStore(
    useShallow((state) => ({
      inputValue: state.inputValue,
      setInputValue: state.setInputValue,
    })),
  );
  const textareaRef = useRef<AutoTextareaRef>(null);

  // Restore unsent message from localStorage on initial load
  useEffect(() => {
    const unsentMessage = localStorage.getItem('unsentChatMessage');
    if (unsentMessage) {
      setInputValue(unsentMessage);
      localStorage.removeItem('unsentChatMessage');
    }
  }, [setInputValue]);

  // Auto-focus the textarea when thread changes or on initial load
  useEffect(() => {
    // Small delay to ensure the component is fully rendered
    const timer = setTimeout(() => {
      if (!isAiTyping) {
        textareaRef.current?.focus();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [currentThreadId, isAiTyping]);

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLTextAreaElement>) => {
      setInputValue(e.target.value);
    },
    [setInputValue],
  );

  const handleEnterKeyPress = useCallback(() => {
    if (!inputValue.trim() || isAiTyping) return;
    onSubmit(inputValue);
    setInputValue('');
    textareaRef.current?.reset();
  }, [inputValue, isAiTyping, onSubmit, setInputValue]);

  const handleSendMessageClick = useCallback(() => {
    handleEnterKeyPress();
  }, [handleEnterKeyPress]);

  return {
    inputValue,
    handleInputChange,
    handleEnterKeyPress,
    handleSendMessageClick,
    textareaRef: textareaRef as Ref<AutoTextareaRef>, // Cast for external use
  };
}
